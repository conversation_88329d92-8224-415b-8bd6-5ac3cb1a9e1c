<?php
session_start();
require_once 'includes/auth.php';
require_admin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Include Database class
require_once '../includes/Database.php';
$db = Database::getInstance();

// Get action from URL
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$faqId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Message handling
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $postAction = $_POST['action'];
        
        if ($postAction === 'add' || $postAction === 'edit') {
            // Validate form data
            $question = trim($_POST['question']);
            $answer = trim($_POST['answer']);
            $category = trim($_POST['category']);
            $displayOrder = (int)$_POST['display_order'];
            $status = $_POST['status'];
            
            if (empty($question) || empty($answer)) {
                $message = 'Question and answer are required fields.';
                $messageType = 'error';
            } else {
                $data = [
                    'question' => $question,
                    'answer' => $answer,
                    'category' => $category,
                    'display_order' => $displayOrder,
                    'status' => $status
                ];
                
                try {
                    if ($postAction === 'add') {
                        $db->insert('faqs', $data);
                        $message = 'FAQ added successfully!';
                    } else {
                        $id = (int)$_POST['id'];
                        $db->update('faqs', $data, 'id = ?', [$id]);
                        $message = 'FAQ updated successfully!';
                    }
                    $messageType = 'success';
                    $action = 'list'; // Redirect to list view
                } catch (Exception $e) {
                    $message = 'Error: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        } elseif ($postAction === 'delete' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            try {
                $db->delete('faqs', 'id = ?', [$id]);
                $message = 'FAQ deleted successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get FAQ data for edit form
$faqData = [];
if (($action === 'edit' || $action === 'view') && $faqId > 0) {
    try {
        $faqData = $db->selectOne('SELECT * FROM faqs WHERE id = ?', [$faqId]);
        if (!$faqData) {
            $message = 'FAQ not found.';
            $messageType = 'error';
            $action = 'list';
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
        $action = 'list';
    }
}

// Get all FAQs for list view
$faqs = [];
if ($action === 'list') {
    try {
        $faqs = $db->select('SELECT * FROM faqs ORDER BY display_order ASC, id ASC');
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get categories for dropdown
$categories = [];
try {
    $categoryResults = $db->select('SELECT DISTINCT category FROM faqs WHERE category IS NOT NULL ORDER BY category');
    foreach ($categoryResults as $cat) {
        $categories[] = $cat['category'];
    }
} catch (Exception $e) {
    // Silently fail
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage FAQs - Doctors At Door Step</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }
        
        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
        }
        
        .add-new-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            background-color: var(--primary-color);
            color: var(--white);
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .add-new-btn:hover {
            background-color: #1a68d1;
            transform: translateY(-2px);
        }
        
        /* Alert Messages */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }
        
        /* Table Styles */
        .faqs-table {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        th {
            background-color: var(--light-color);
            font-weight: 600;
        }
        
        tr:hover {
            background-color: rgba(44, 123, 229, 0.05);
        }
        
        .faq-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #D1FAE5;
            color: #059669;
        }
        
        .status-inactive {
            background-color: #FEE2E2;
            color: #DC2626;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-action {
            padding: 5px 10px;
            border-radius: 5px;
            color: var(--white);
            text-decoration: none;
            font-size: 12px;
            cursor: pointer;
            border: none;
        }
        
        .btn-view {
            background-color: var(--secondary-color);
        }
        
        .btn-edit {
            background-color: #3B82F6;
        }
        
        .btn-delete {
            background-color: #EF4444;
        }
        
        /* Form Styles */
        .form-container {
            background-color: var(--white);
            border-radius: 10px;
            padding: 30px;
            box-shadow: var(--box-shadow);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        
        .btn-submit {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            background-color: #1a68d1;
        }
        
        .btn-cancel {
            padding: 10px 20px;
            background-color: #e0e0e0;
            color: var(--dark-color);
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-cancel:hover {
            background-color: #d0d0d0;
        }
        
        /* Responsive */
        @media (max-width: 991px) {
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <?php if ($action === 'add'): ?>
                    Add New FAQ
                <?php elseif ($action === 'edit'): ?>
                    Edit FAQ
                <?php elseif ($action === 'view'): ?>
                    View FAQ
                <?php else: ?>
                    Manage FAQs
                <?php endif; ?>
            </h1>
            
            <?php if ($action === 'list'): ?>
                <a href="faqs.php?action=add" class="add-new-btn">
                    <i class="fas fa-plus"></i> Add New FAQ
                </a>
            <?php endif; ?>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <!-- FAQs List -->
            <div class="faqs-table">
                <table>
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Question</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($faqs)): ?>
                            <tr>
                                <td colspan="5" style="text-align: center;">No FAQs found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($faqs as $faq): ?>
                                <tr>
                                    <td><?php echo $faq['display_order']; ?></td>
                                    <td><?php echo htmlspecialchars($faq['question']); ?></td>
                                    <td><?php echo htmlspecialchars($faq['category']); ?></td>
                                    <td>
                                        <span class="faq-status status-<?php echo $faq['status']; ?>">
                                            <?php echo ucfirst($faq['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="faqs.php?action=view&id=<?php echo $faq['id']; ?>" class="btn-action btn-view">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="faqs.php?action=edit&id=<?php echo $faq['id']; ?>" class="btn-action btn-edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this FAQ?');">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $faq['id']; ?>">
                                                <button type="submit" class="btn-action btn-delete">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- FAQ Form -->
            <div class="form-container">
                <form method="POST">
                    <input type="hidden" name="action" value="<?php echo $action; ?>">
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="id" value="<?php echo $faqData['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="question" class="form-label">Question</label>
                        <input type="text" id="question" name="question" class="form-control" value="<?php echo $action === 'edit' ? htmlspecialchars($faqData['question']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="answer" class="form-label">Answer</label>
                        <textarea id="answer" name="answer" class="form-control" rows="6" required><?php echo $action === 'edit' ? htmlspecialchars($faqData['answer']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="category" class="form-label">Category</label>
                        <input type="text" id="category" name="category" class="form-control" list="category-list" value="<?php echo $action === 'edit' ? htmlspecialchars($faqData['category']) : ''; ?>">
                        <datalist id="category-list">
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo htmlspecialchars($category); ?>">
                            <?php endforeach; ?>
                        </datalist>
                    </div>
                    
                    <div class="form-group">
                        <label for="display_order" class="form-label">Display Order</label>
                        <input type="number" id="display_order" name="display_order" class="form-control" value="<?php echo $action === 'edit' ? (int)$faqData['display_order'] : count($faqs) + 1; ?>" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active" <?php echo ($action === 'edit' && $faqData['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($action === 'edit' && $faqData['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-submit">
                            <?php echo $action === 'add' ? 'Add FAQ' : 'Update FAQ'; ?>
                        </button>
                        <a href="faqs.php" class="btn-cancel">Cancel</a>
                    </div>
                </form>
            </div>
        <?php elseif ($action === 'view'): ?>
            <!-- FAQ View -->
            <div class="form-container">
                <div class="form-group">
                    <label class="form-label">Question</label>
                    <div><?php echo htmlspecialchars($faqData['question']); ?></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Answer</label>
                    <div><?php echo $faqData['answer']; ?></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Category</label>
                    <div><?php echo htmlspecialchars($faqData['category']); ?></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Display Order</label>
                    <div><?php echo (int)$faqData['display_order']; ?></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <div>
                        <span class="faq-status status-<?php echo $faqData['status']; ?>">
                            <?php echo ucfirst($faqData['status']); ?>
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Created At</label>
                    <div><?php echo date('F j, Y, g:i a', strtotime($faqData['created_at'])); ?></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Last Updated</label>
                    <div><?php echo date('F j, Y, g:i a', strtotime($faqData['updated_at'])); ?></div>
                </div>
                
                <div class="form-actions">
                    <a href="faqs.php?action=edit&id=<?php echo $faqData['id']; ?>" class="btn-submit">Edit</a>
                    <a href="faqs.php" class="btn-cancel">Back to List</a>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Summernote editor
            $('#answer').summernote({
                placeholder: 'Enter the answer here...',
                tabsize: 2,
                height: 200,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ]
            });
        });
    </script>
</body>
</html>
