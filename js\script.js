// DOM Elements
const header = document.getElementById('header');
const navToggle = document.getElementById('navToggle');
const navMenu = document.querySelector('.nav-menu');
const newsletterForm = document.getElementById('newsletterForm');

// Mobile Menu Toggle
navToggle.addEventListener('click', () => {
    navToggle.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking outside
document.addEventListener('click', (e) => {
    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
        navToggle.classList.remove('active');
        navMenu.classList.remove('active');
    }
});

// Sticky Header
let lastScroll = 0;
window.addEventListener('scroll', () => {
    const currentScroll = window.pageYOffset;

    if (currentScroll <= 0) {
        header.classList.remove('scroll-up');
        return;
    }

    if (currentScroll > lastScroll && !header.classList.contains('scroll-down')) {
        // Scrolling down
        header.classList.remove('scroll-up');
        header.classList.add('scroll-down');
    } else if (currentScroll < lastScroll && header.classList.contains('scroll-down')) {
        // Scrolling up
        header.classList.remove('scroll-down');
        header.classList.add('scroll-up');
    }
    lastScroll = currentScroll;
});

// Initialize AOS and Hero Slider
document.addEventListener('DOMContentLoaded', () => {
    // Fix for service cards - remove AOS attributes
    const serviceCards = document.querySelectorAll('.service-card');
    if (serviceCards.length > 0) {
        serviceCards.forEach(card => {
            // Remove AOS attributes from service cards to prevent conflicts with GSAP
            card.removeAttribute('data-aos');
            card.removeAttribute('data-aos-delay');
        });
    }

    // Initialize AOS for other elements
    AOS.init({
        duration: 800,
        once: true,
        offset: 100
    });

    // Initialize Hero Background Slider
    initHeroSlider();

    // Initialize hero parallax effect
    initHeroParallax();

    // Initialize hero text animations
    initHeroTextAnimations();
});

// Hero parallax effect
function initHeroParallax() {
    const hero = document.querySelector('.hero');
    const heroContent = document.querySelector('.hero-content');
    const floatingElements = document.querySelectorAll('.floating-element');

    if (hero && heroContent) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            const rate2 = scrolled * -0.3;

            // Parallax effect for hero content
            if (scrolled < window.innerHeight) {
                heroContent.style.transform = `translateY(${rate2}px)`;

                // Parallax effect for floating elements
                floatingElements.forEach((element, index) => {
                    const speed = 0.2 + (index * 0.1);
                    element.style.transform = `translateY(${scrolled * speed}px)`;
                });
            }
        });
    }
}

// Hero text animations
function initHeroTextAnimations() {
    // Simple fade-in animations are handled by AOS
    // No complex text manipulation needed
}

// Enhanced Hero Background Slider Initialization
function initHeroSlider() {
    const heroSwiper = document.querySelector('.hero-swiper');
    if (heroSwiper && typeof Swiper !== 'undefined') {
        const heroSlider = new Swiper('.hero-swiper', {
            // Basic settings
            slidesPerView: 1,
            spaceBetween: 0,
            loop: true,
            speed: 1200,

            // Autoplay
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },

            // Effects
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },

            // Pagination
            pagination: {
                el: '.hero-slider-dots',
                clickable: true,
                bulletClass: 'swiper-pagination-bullet',
                bulletActiveClass: 'swiper-pagination-bullet-active',
            },

            // Navigation
            navigation: {
                nextEl: '.hero-next',
                prevEl: '.hero-prev',
            },

            // Accessibility
            a11y: {
                prevSlideMessage: 'Previous slide',
                nextSlideMessage: 'Next slide',
            },

            // Events
            on: {
                init: function() {
                    console.log('Enhanced hero slider initialized');
                    updateProgressBar(this);
                    animateSlideContent(this);
                },
                slideChange: function() {
                    updateProgressBar(this);
                    animateSlideContent(this);
                },
                autoplayTimeLeft: function(s, time, progress) {
                    updateProgressBar(this, progress);
                }
            }
        });

        // Initialize scroll indicator click
        initScrollIndicator();

    } else if (!heroSwiper) {
        console.log('Hero slider element not found');
    } else {
        console.error('Swiper library not loaded');
    }
}

// Update progress bar
function updateProgressBar(swiper, progress = 0) {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        if (progress > 0) {
            // During autoplay
            progressBar.style.width = (100 - progress * 100) + '%';
        } else {
            // On slide change
            progressBar.style.width = '0%';
            setTimeout(() => {
                progressBar.style.width = '100%';
                progressBar.style.transition = 'width ' + (swiper.autoplay.delay / 1000) + 's linear';
            }, 100);
        }
    }
}

// Animate slide content
function animateSlideContent(swiper) {
    const activeSlide = swiper.slides[swiper.activeIndex];
    const slideBadge = activeSlide.querySelector('.slide-badge');

    if (slideBadge) {
        slideBadge.style.animation = 'none';
        setTimeout(() => {
            slideBadge.style.animation = 'slideInRight 1s ease-out';
        }, 100);
    }
}

// Initialize scroll indicator
function initScrollIndicator() {
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            const nextSection = document.querySelector('.services-overview') ||
                              document.querySelector('.why-choose-us') ||
                              document.querySelector('main > section:first-child');
            if (nextSection) {
                nextSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
}

// GSAP Animations
document.addEventListener('DOMContentLoaded', () => {
    // Register ScrollTrigger
    gsap.registerPlugin(ScrollTrigger);

    // Animate stats counter
    const statsNumbers = document.querySelectorAll('.stats-number');
    if (statsNumbers.length > 0) {
        statsNumbers.forEach(stat => {
            const value = parseFloat(stat.getAttribute('data-value'));
            gsap.to(stat, {
                textContent: value,
                duration: 2,
                ease: "power2.out",
                snap: { textContent: 1 },
                scrollTrigger: {
                    trigger: stat,
                    start: "top 80%",
                },
                onUpdate: function() {
                    stat.textContent = Math.round(stat.textContent);
                }
            });
        });
    }

// Animate service cards
const serviceCards = document.querySelectorAll('.service-card');
if (serviceCards.length > 0) {
    // Make sure service cards are visible initially
    serviceCards.forEach(card => {
        card.style.opacity = 1;
    });

    // Then apply GSAP animation
    gsap.from(serviceCards, {
        y: 60,
        opacity: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
            trigger: ".services-grid",
            start: "top 80%",
        }
    });
}

    // Handle team members
    const teamMembers = document.querySelectorAll('.team-member');
    if (teamMembers.length > 0) {
        // Remove AOS attributes and make them visible
        teamMembers.forEach(member => {
            member.removeAttribute('data-aos');
            member.removeAttribute('data-aos-delay');
            member.style.opacity = 1;
        });

        // Apply GSAP animation
        gsap.from(teamMembers, {
            scale: 0.8,
            opacity: 0,
            duration: 1,
            stagger: 0.2,
            ease: "back.out(1.7)",
            scrollTrigger: {
                trigger: ".team-grid",
                start: "top 80%",
            }
        });
    }
});

// Newsletter Form Submission
if (newsletterForm) {
    newsletterForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const emailInput = newsletterForm.querySelector('input[type="email"]');
        const email = emailInput.value;

        try {
            // Here you would typically send this to your backend
            console.log('Newsletter subscription:', email);

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'newsletter-success';
            successMessage.textContent = 'Thank you for subscribing!';

            // Replace form with success message
            newsletterForm.innerHTML = '';
            newsletterForm.appendChild(successMessage);

        } catch (error) {
            console.error('Newsletter submission error:', error);
            alert('There was an error subscribing to the newsletter. Please try again.');
        }
    });
}

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            // Close mobile menu if open
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });
});

// FAQ Accordion (for custom FAQ items, not Bootstrap accordion)
const faqItems = document.querySelectorAll('.faq-item');
if (faqItems.length > 0) {
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        if (question) {
            question.addEventListener('click', () => {
                // Close all other FAQ items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        otherItem.classList.remove('active');
                    }
                });
                // Toggle current FAQ item
                item.classList.toggle('active');
            });
        }
    });
}

// Add active class to current nav link
const currentPage = window.location.pathname;
const navLinks = document.querySelectorAll('.nav-link');
navLinks.forEach(link => {
    if (link.getAttribute('href') === currentPage.split('/').pop()) {
        link.classList.add('active');
    }
});

// Lazy loading for images
if ('loading' in HTMLImageElement.prototype) {
    const images = document.querySelectorAll('img[loading="lazy"]');
    images.forEach(img => {
        // Only update src if data-src exists
        if (img.dataset.src) {
            img.src = img.dataset.src;
        }
    });
} else {
    // Fallback for browsers that don't support lazy loading
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js';
    document.body.appendChild(script);
}
/* Scroll to Top Button */
document.addEventListener('DOMContentLoaded', () => {
    // Create scroll to top button
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.id = 'scrollToTopBtn';
    scrollToTopBtn.classList.add('scroll-to-top');
    scrollToTopBtn.setAttribute('aria-label', 'Scroll to top');
    scrollToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    document.body.appendChild(scrollToTopBtn);

    // Function to check scroll position and toggle button visibility
    const toggleScrollButton = () => {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('show');
        } else {
            scrollToTopBtn.classList.remove('show');
        }
    };

    // Function to scroll to top smoothly
    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    // Add scroll event listener
    window.addEventListener('scroll', toggleScrollButton);

    // Add click event listener
    scrollToTopBtn.addEventListener('click', scrollToTop);

    // Clean up event listeners (optional, but good practice)
    return () => {
        window.removeEventListener('scroll', toggleScrollButton);
        scrollToTopBtn.removeEventListener('click', scrollToTop);
    };
});

// Initialize scroll hint functionality
function initScrollHint() {
    const scrollHint = document.querySelector('.scroll-hint');
    if (scrollHint) {
        scrollHint.addEventListener('click', function() {
            const nextSection = document.querySelector('.page-hero').nextElementSibling;
            if (nextSection) {
                nextSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });

        // Hide scroll hint on scroll
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            if (window.scrollY > 100) {
                scrollHint.style.opacity = '0';
                scrollHint.style.transform = 'translateX(-50%) translateY(20px)';
            } else {
                scrollHint.style.opacity = '0.7';
                scrollHint.style.transform = 'translateX(-50%) translateY(0)';
            }
        });
    }
}

// Enhanced page hero animations
function initPageHeroAnimations() {
    const pageHero = document.querySelector('.page-hero');
    if (pageHero && window.innerWidth > 768) {
        // Add subtle mouse move effect
        pageHero.addEventListener('mousemove', function(e) {
            const container = pageHero.querySelector('.container');
            if (container) {
                const rect = pageHero.getBoundingClientRect();
                const x = (e.clientX - rect.left) / rect.width;
                const y = (e.clientY - rect.top) / rect.height;

                const moveX = (x - 0.5) * 10;
                const moveY = (y - 0.5) * 10;

                container.style.transform = `translate(${moveX}px, ${moveY}px)`;
            }
        });

        pageHero.addEventListener('mouseleave', function() {
            const container = pageHero.querySelector('.container');
            if (container) {
                container.style.transform = 'translate(0, 0)';
            }
        });
    }
}

// Initialize enhanced hero features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initScrollHint();
    initPageHeroAnimations();
});