<?php
session_start();
require_once 'includes/auth.php';
require_admin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

require_once '../includes/Blog.php';

// Initialize Blog handler
$blogHandler = new Blog();
$message = '';
$messageType = '';

// Handle post status change
if (isset($_GET['action']) && $_GET['action'] === 'change_status' && isset($_GET['id']) && isset($_GET['status'])) {
    $post_id = (int)$_GET['id'];
    $new_status = $_GET['status'];

    try {
        // The updatePostStatus method now throws an exception if it fails
        $blogHandler->updatePostStatus($post_id, $new_status);
        $message = "Post status updated successfully!";
        $messageType = 'success';
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $post_id = (int)$_GET['id'];

    try {
        // The deletePost method now throws an exception if it fails
        $blogHandler->deletePost($post_id);
        $message = "Post deleted successfully!";
        $messageType = 'success';
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 10;

// Get posts based on filters
$posts = $blogHandler->getAdminPosts($search, $status, $category, $page, $per_page);
$total_posts = $blogHandler->countAdminPosts($search, $status, $category);
$total_pages = ceil($total_posts / $per_page);

// Get all categories for filter dropdown
$categories = $blogHandler->getCategories();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Blog Posts - HomeCare</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
            --warning-color: #F59E0B;
            --info-color: #3B82F6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--dark-color);
            color: var(--white);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 20px;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: rgba(44, 123, 229, 0.2);
            color: var(--primary-color);
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: absolute;
            bottom: 0;
            width: 100%;
        }

        .logout-btn {
            display: block;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
            overflow: visible; /* Ensure dropdowns aren't clipped */
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        .add-new-btn {
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s;
        }

        .add-new-btn:hover {
            background-color: var(--dark-color);
        }

        /* Alert Messages */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }        /* Filter Section */
        .filter-section {
            background-color: var(--white);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .filter-form .form-group {
            flex: 1;
            min-width: 200px;
            margin-bottom: 0;
        }

        .filter-form .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--secondary-color);
            font-size: 14px;
        }

        .filter-form .form-group input,
        .filter-form .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .filter-form .form-group input:focus,
        .filter-form .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(44, 123, 229, 0.1);
        }

        .filter-form .form-actions {
            margin-top: 0;
            display: flex;
            gap: 10px;
        }

        /* Blog Posts Table */        .posts-table {
            width: 100%;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            overflow: visible; /* Changed from hidden to visible */
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .posts-table table {
            width: 100%;
            border-collapse: collapse;
            overflow: visible; /* Ensure table doesn't clip dropdowns */
        }        .posts-table th, .posts-table td {
            padding: 16px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            vertical-align: middle;
            position: relative; /* Enable relative positioning for dropdown parent */
            overflow: visible; /* Ensure content can overflow */
        }

        /* Special handling for the actions column */
        .posts-table td:last-child {
            overflow: visible;
            position: relative;
        }

        .posts-table th {
            background-color: #f8fafc;
            font-weight: 600;
            font-size: 13px;
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .posts-table tr:last-child td {
            border-bottom: none;
        }        .posts-table tbody tr {
            transition: all 0.2s ease;
            position: relative; /* Enable relative positioning for dropdown positioning */
        }

        .posts-table tbody tr:hover {
            background-color: rgba(44, 123, 229, 0.03);
            z-index: 1; /* Raise hovered row slightly */
        }

        /* Ensure the row with open dropdown has higher z-index */
        .posts-table tbody tr:has(.dropdown-menu.show) {
            z-index: 999;
        }

        .post-title {
            font-weight: 500;
            color: var(--dark-color);
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }        .post-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-published {
            background-color: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-draft {
            background-color: rgba(107, 122, 153, 0.1);
            color: #64748b;
            border: 1px solid rgba(107, 122, 153, 0.2);
        }

        .status-archived {
            background-color: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }        .btn-view, .btn-edit, .btn-delete, .btn-status {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            outline: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-view {
            background-color: rgba(107, 122, 153, 0.1);
            color: var(--secondary-color);
        }

        .btn-view:hover {
            background-color: var(--secondary-color);
            color: var(--white);
            transform: translateY(-1px);
        }

        .btn-edit {
            background-color: rgba(44, 123, 229, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(44, 123, 229, 0.2);
        }

        .btn-edit:hover {
            background-color: var(--primary-color);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(44, 123, 229, 0.3);
        }

        .btn-status {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .btn-status:hover {
            background-color: var(--warning-color);
            color: var(--white);
            transform: translateY(-1px);
        }

        .btn-delete {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .btn-delete:hover {
            background-color: var(--error-color);
            color: var(--white);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 35px;
            height: 35px;
            padding: 0 10px;
            background-color: var(--white);
            color: var(--dark-color);
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: var(--box-shadow-sm);
            transition: all 0.3s;
        }

        .pagination-item:hover {
            background-color: var(--light-color);
        }

        .pagination-item.active {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .pagination-ellipsis {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 35px;
            height: 35px;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
        }

        .form-actions {
            display: flex;
            justify-content: flex-start;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-cancel {
            padding: 10px 20px;
            background-color: var(--light-color);
            color: var(--dark-color);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-cancel:hover {
            background-color: #e0e0e0;
        }

        .btn-submit {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-submit:hover {
            background-color: var(--dark-color);
        }

        /* Empty State */
        .empty-state {
            padding: 50px 20px;
            text-align: center;
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--box-shadow);
        }

        .empty-state i {
            font-size: 48px;
            color: var(--secondary-color);
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: var(--secondary-color);
            margin-bottom: 20px;
        }        /* Dropdown styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: var(--white);
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-top: 5px;
            overflow: hidden;
        }

        /* Smart positioning - if dropdown is near bottom, show it above */
        .dropdown-menu.show-above {
            top: auto;
            bottom: 100%;
            margin-top: 0;
            margin-bottom: 5px;
        }

        /* Ensure dropdown stays within viewport */
        .dropdown-menu.align-left {
            right: auto;
            left: 0;
        }

        .dropdown-menu::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 15px;
            width: 12px;
            height: 12px;
            background-color: var(--white);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: none;
            border-right: none;
            transform: rotate(45deg);
        }

        /* Arrow positioning for dropdowns shown above */
        .dropdown-menu.show-above::before {
            top: auto;
            bottom: -6px;
            border-top: none;
            border-left: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            transform: rotate(45deg);
        }

        /* Arrow positioning for left-aligned dropdowns */
        .dropdown-menu.align-left::before {
            right: auto;
            left: 15px;
        }

        .dropdown-menu.show {
            display: block;
            animation: dropdownFadeIn 0.2s ease-out forwards;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--dark-color);
            text-decoration: none;
            font-size: 14px;
            font-weight: 400;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background-color: #f8fafc;
            color: var(--primary-color);
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
            font-size: 14px;
        }        .text-danger {
            color: var(--error-color) !important;
        }

        .text-danger:hover {
            background-color: rgba(239, 68, 68, 0.1) !important;
            color: var(--error-color) !important;
        }        /* Loading State */
        .loading {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced hover effects */
        .dropdown-item.status-action {
            font-weight: 500;
        }

        .dropdown-item.status-action.publish {
            color: var(--success-color);
        }

        .dropdown-item.status-action.publish:hover {
            background-color: rgba(16, 185, 129, 0.1);
        }

        .dropdown-item.status-action.draft {
            color: var(--secondary-color);
        }

        .dropdown-item.status-action.draft:hover {
            background-color: rgba(107, 122, 153, 0.1);
        }

        .dropdown-item.status-action.archive {
            color: var(--warning-color);
        }

        .dropdown-item.status-action.archive:hover {
            background-color: rgba(245, 158, 11, 0.1);
        }

        /* Mobile toggle button */
        .toggle-sidebar {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            padding: 10px;
            cursor: pointer;
        }

        /* Responsive */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding-top: 70px;
            }

            .toggle-sidebar {
                display: block;
            }

            .posts-table {
                overflow-x: auto;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .filter-form {
                flex-direction: column;
            }

            .filter-form .form-group {
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 15px;
            }

            .filter-section, .posts-table {
                padding: 15px;
            }

            .posts-table th, .posts-table td {
                padding: 10px 8px;
                font-size: 14px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Mobile Toggle Button -->
    <button class="toggle-sidebar">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">Manage Blog Posts</h1>
            <a href="blog-edit.php" class="add-new-btn">
                <i class="fas fa-plus"></i> Add New Post
            </a>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Filter Section -->
        <div class="filter-section">
            <form action="blog-management.php" method="GET" class="filter-form">
                <div class="form-group">
                    <label for="search">Search</label>
                    <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search posts...">
                </div>

                <div class="form-group">
                    <label for="status">Status</label>
                    <select id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>Published</option>
                        <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                        <option value="archived" <?php echo $status === 'archived' ? 'selected' : ''; ?>>Archived</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="category">Category</label>
                    <select id="category" name="category">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-submit">Filter</button>
                    <a href="blog-management.php" class="btn-cancel">Reset</a>
                </div>
            </form>
        </div>

        <?php if (empty($posts)): ?>
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>No posts found</h3>
                <p>Try adjusting your search or filter criteria, or add a new post.</p>
                <a href="blog-edit.php" class="btn-submit">Add New Post</a>
            </div>
        <?php else: ?>
            <!-- Blog Posts Table -->
            <div class="posts-table">
                <table>
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Author</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($posts as $post): ?>
                            <tr>
                                <td class="post-title"><?php echo htmlspecialchars($post['title']); ?></td>
                                <td><?php echo htmlspecialchars($post['category_name']); ?></td>
                                <td><?php echo htmlspecialchars($post['author_name']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($post['created_at'])); ?></td>
                                <td>
                                    <span class="post-status status-<?php echo $post['status']; ?>">
                                        <?php echo ucfirst($post['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn-edit" onclick="toggleDropdown(<?php echo $post['id']; ?>)">
                                            Actions <i class="fas fa-chevron-down"></i>
                                        </button>                                        <div class="dropdown-menu" id="dropdown-<?php echo $post['id']; ?>">
                                            <a href="../blog-post.php?slug=<?php echo $post['slug']; ?>" class="dropdown-item" target="_blank">
                                                <i class="fas fa-eye"></i> View Post
                                            </a>
                                            <a href="blog-edit.php?id=<?php echo $post['id']; ?>" class="dropdown-item">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>

                                            <?php if ($post['status'] !== 'published'): ?>
                                                <a href="blog-management.php?action=change_status&id=<?php echo $post['id']; ?>&status=published" class="dropdown-item status-action publish">
                                                    <i class="fas fa-check-circle"></i> Publish
                                                </a>
                                            <?php endif; ?>

                                            <?php if ($post['status'] !== 'draft'): ?>
                                                <a href="blog-management.php?action=change_status&id=<?php echo $post['id']; ?>&status=draft" class="dropdown-item status-action draft">
                                                    <i class="fas fa-file"></i> Move to Draft
                                                </a>
                                            <?php endif; ?>

                                            <?php if ($post['status'] !== 'archived'): ?>
                                                <a href="blog-management.php?action=change_status&id=<?php echo $post['id']; ?>&status=archived" class="dropdown-item status-action archive">
                                                    <i class="fas fa-archive"></i> Archive
                                                </a>
                                            <?php endif; ?>

                                            <div style="border-top: 1px solid rgba(0,0,0,0.1); margin: 4px 0;"></div>
                                            <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $post['id']; ?>, '<?php echo addslashes($post['title']); ?>')" class="dropdown-item text-danger">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-item">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1) {
                        echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => 1])) . '" class="pagination-item">1</a>';
                        if ($start_page > 2) {
                            echo '<span class="pagination-ellipsis">...</span>';
                        }
                    }

                    for ($i = $start_page; $i <= $end_page; $i++) {
                        $active = $i == $page ? 'active' : '';
                        echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $i])) . '" class="pagination-item ' . $active . '">' . $i . '</a>';
                    }

                    if ($end_page < $total_pages) {
                        if ($end_page < $total_pages - 1) {
                            echo '<span class="pagination-ellipsis">...</span>';
                        }
                        echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $total_pages])) . '" class="pagination-item">' . $total_pages . '</a>';
                    }
                    ?>

                    <?php if ($page < $total_pages): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-item">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>    <script>
        // Improved dropdown handling with smart positioning
        let currentOpenDropdown = null;        function toggleDropdown(postId) {
            const dropdown = document.getElementById(`dropdown-${postId}`);
            const button = dropdown.previousElementSibling;
            const tableRow = button.closest('tr');
            
            // Close any currently open dropdown
            if (currentOpenDropdown && currentOpenDropdown !== dropdown) {
                currentOpenDropdown.classList.remove('show', 'show-above', 'align-left');
                // Remove z-index from previous row
                const prevRow = currentOpenDropdown.closest('tr');
                if (prevRow) prevRow.style.zIndex = '';
            }
            
            if (dropdown.classList.contains('show')) {
                // Close the dropdown
                dropdown.classList.remove('show', 'show-above', 'align-left');
                tableRow.style.zIndex = ''; // Reset z-index
                currentOpenDropdown = null;
            } else {
                // Open the dropdown with smart positioning
                const rect = button.getBoundingClientRect();
                const dropdownHeight = 200; // Approximate dropdown height
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;
                
                // Reset classes first
                dropdown.classList.remove('show-above', 'align-left');
                
                // Check if dropdown would go off-screen vertically
                if (rect.bottom + dropdownHeight > viewportHeight) {
                    dropdown.classList.add('show-above');
                }
                
                // Check if dropdown would go off-screen horizontally
                if (rect.right - 180 < 0) { // 180px is dropdown width
                    dropdown.classList.add('align-left');
                }
                
                // Raise the table row z-index
                tableRow.style.zIndex = '999';
                
                // Show the dropdown
                dropdown.classList.add('show');
                currentOpenDropdown = dropdown;
            }
        }        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown') && currentOpenDropdown) {
                currentOpenDropdown.classList.remove('show', 'show-above', 'align-left');
                // Reset table row z-index
                const tableRow = currentOpenDropdown.closest('tr');
                if (tableRow) tableRow.style.zIndex = '';
                currentOpenDropdown = null;
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && currentOpenDropdown) {
                currentOpenDropdown.classList.remove('show', 'show-above', 'align-left');
                // Reset table row z-index
                const tableRow = currentOpenDropdown.closest('tr');
                if (tableRow) tableRow.style.zIndex = '';
                currentOpenDropdown = null;
            }
        });

        // Reposition dropdowns on window resize
        window.addEventListener('resize', function() {
            if (currentOpenDropdown) {
                currentOpenDropdown.classList.remove('show', 'show-above', 'align-left');
                // Reset table row z-index
                const tableRow = currentOpenDropdown.closest('tr');
                if (tableRow) tableRow.style.zIndex = '';
                currentOpenDropdown = null;
            }
        });

        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });

        // Enhanced confirm delete with better styling
        function confirmDelete(id, title) {
            const confirmed = confirm(`⚠️ Delete Post\n\nAre you sure you want to delete "${title}"?\n\nThis action cannot be undone.`);
            if (confirmed) {
                window.location.href = `blog-management.php?action=delete&id=${id}`;
            }
        }

        // Auto-hide success/error messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const messages = document.querySelectorAll('.message');
            messages.forEach(message => {
                setTimeout(() => {
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.style.display = 'none';
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>