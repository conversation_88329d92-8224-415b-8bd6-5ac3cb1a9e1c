<?php
class Team {
    private $db;
    private $lastError = null;

    public function __construct($db) {
        $this->db = $db;
        
        // Create table if it doesn't exist
        if (!$this->checkTableExists()) {
            error_log("Team members table not found, attempting to create it");
            $this->createTableIfNotExists();
        }
    }

    public function getAllActiveMembers() {
        $sql = "SELECT * FROM team_members WHERE is_active = 1 ORDER BY display_order ASC";
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching team members: " . $e->getMessage());
            return [];
        }
    }

    public function getMemberById($id) {
        $sql = "SELECT * FROM team_members WHERE id = :id";
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute(['id' => $id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching team member: " . $e->getMessage());
            return null;
        }
    }    public function getLastError() {
        return $this->lastError;
    }

    public function createMember($data) {
        $this->lastError = null; // Reset error
        
        // First check if table exists
        if (!$this->checkTableExists()) {
            $this->lastError = "Team members table does not exist";
            error_log("Team members table does not exist");
            return false;
        }

        $sql = "INSERT INTO team_members (name, position, bio, photo_path, specialties, qualifications, is_active, display_order) 
                VALUES (:name, :position, :bio, :photo_path, :specialties, :qualifications, :is_active, :display_order)";
        try {
            $stmt = $this->db->prepare($sql);
            $params = [
                'name' => trim($data['name']),
                'position' => trim($data['position']),
                'bio' => trim($data['bio'] ?? ''),
                'photo_path' => $data['photo_path'] ?? null,
                'specialties' => trim($data['specialties'] ?? ''),
                'qualifications' => trim($data['qualifications'] ?? ''),
                'is_active' => $data['is_active'] ? 1 : 0,
                'display_order' => (int)($data['display_order'] ?? 0)
            ];
            
            // Log the attempt with more details
            error_log("=== TEAM MEMBER CREATION ATTEMPT ===");
            error_log("SQL: " . $sql);
            error_log("Parameters: " . json_encode($params));
            error_log("PDO Error Mode: " . $this->db->getAttribute(PDO::ATTR_ERRMODE));
            error_log("Database Name: " . $this->db->query('SELECT DATABASE()')->fetchColumn());
            
            $result = $stmt->execute($params);
            
            if ($result) {
                $insertId = $this->db->lastInsertId();
                error_log("Team member created successfully with ID: " . $insertId);
                
                // Verify the insertion
                $verifyStmt = $this->db->prepare("SELECT COUNT(*) FROM team_members WHERE id = ?");
                $verifyStmt->execute([$insertId]);
                $count = $verifyStmt->fetchColumn();
                error_log("Verification count: " . $count);
                
                return true;
            } else {
                $errorInfo = $stmt->errorInfo();
                $this->lastError = "SQL Error: " . ($errorInfo[2] ?? 'Unknown error');
                error_log("Failed to execute team member insertion");
                error_log("Statement Error Info: " . json_encode($errorInfo));
                return false;
            }
        } catch (PDOException $e) {
            $this->lastError = "Database Error: " . $e->getMessage();
            error_log("PDOException creating team member: " . $e->getMessage());
            error_log("Error Code: " . $e->getCode());
            error_log("SQL State: " . $e->errorInfo[0] ?? 'N/A');
            error_log("Driver Error Code: " . $e->errorInfo[1] ?? 'N/A');
            error_log("Driver Error Message: " . $e->errorInfo[2] ?? 'N/A');
            error_log("SQL: " . $sql);
            error_log("Parameters: " . json_encode($params ?? []));
            return false;
        }
    }

    public function updateMember($id, $data) {
        $sql = "UPDATE team_members SET 
                name = :name, 
                position = :position, 
                bio = :bio, 
                specialties = :specialties, 
                qualifications = :qualifications, 
                is_active = :is_active, 
                display_order = :display_order";
        
        if (isset($data['photo_path'])) {
            $sql .= ", photo_path = :photo_path";
        }
        
        $sql .= " WHERE id = :id";

        try {
            $stmt = $this->db->prepare($sql);
            $params = [
                'id' => $id,
                'name' => $data['name'],
                'position' => $data['position'],
                'bio' => $data['bio'],
                'specialties' => $data['specialties'] ?? null,
                'qualifications' => $data['qualifications'] ?? null,
                'is_active' => $data['is_active'] ? 1 : 0,
                'display_order' => $data['display_order'] ?? 0
            ];

            if (isset($data['photo_path'])) {
                $params['photo_path'] = $data['photo_path'];
            }

            $stmt->execute($params);
            return true;
        } catch (PDOException $e) {
            error_log("Error updating team member: " . $e->getMessage());
            return false;
        }
    }

    public function deleteMember($id) {
        $sql = "DELETE FROM team_members WHERE id = :id";
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute(['id' => $id]);
            return true;
        } catch (PDOException $e) {
            error_log("Error deleting team member: " . $e->getMessage());
            return false;
        }
    }

    private function checkTableExists() {
        try {
            $stmt = $this->db->prepare("SHOW TABLES LIKE 'team_members'");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result !== false;
        } catch (PDOException $e) {
            error_log("Error checking if team_members table exists: " . $e->getMessage());
            return false;
        }
    }

    public function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS `team_members` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `position` varchar(100) NOT NULL,
          `bio` text DEFAULT NULL,
          `photo_path` varchar(255) DEFAULT NULL,
          `specialties` text DEFAULT NULL,
          `qualifications` text DEFAULT NULL,
          `is_active` tinyint(1) NOT NULL DEFAULT 1,
          `display_order` int(11) NOT NULL DEFAULT 0,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `is_active` (`is_active`),
          KEY `display_order` (`display_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $this->db->exec($sql);
            error_log("Team members table created successfully");
            return true;
        } catch (PDOException $e) {
            error_log("Error creating team_members table: " . $e->getMessage());
            return false;
        }
    }
}
?>
