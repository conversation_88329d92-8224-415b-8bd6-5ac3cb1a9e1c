<?php
require_once 'includes/Blog.php';

// Initialize Blog handler
$blogHandler = new Blog();

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 6; // Number of posts per page

// Get query parameters for filtering (simplified - only search)
$search_term = isset($_GET['search']) ? $_GET['search'] : null;

// Get posts - newest first, with optional search (with fixed image paths)
if ($search_term) {
    $posts = $blogHandler->searchPostsForDisplay($search_term, $page, $per_page);
    $total_posts = $blogHandler->countSearchResults($search_term);
    $page_title = "Search Results for: " . htmlspecialchars($search_term);
    $meta_description = "Search results for '" . htmlspecialchars($search_term) . "' on our blog.";
} else {
    $posts = $blogHandler->getPostsForDisplay($page, $per_page);
    $total_posts = $blogHandler->countPosts();
    $page_title = "Blog";
    $meta_description = "Read our latest blog posts about healthcare, medical services, and health tips.";
}

// Set global variables for header.php to use
$GLOBALS['page_title'] = $page_title;
$GLOBALS['page_description'] = $meta_description;

// Calculate total pages for pagination
$total_pages = ceil($total_posts / $per_page);

// Get recent posts for sidebar with fixed image paths
$recent_posts = $blogHandler->getRecentPostsForDisplay(8);

include 'includes/header.php';
?>

<!-- Hero Banner -->
<section class="page-hero blog-hero" data-aos="fade-up" style="background-image: linear-gradient(rgba(26, 43, 60, 0.8), rgba(26, 43, 60, 0.8)), url('images/blog-banner.jpg');">
    <div class="container">
        <div class="breadcrumb">
            <a href="index.php">Home</a>
            <span class="separator">/</span>
            <span>Blog</span>
        </div>
        <h1><?php echo $page_title; ?></h1>
        <?php if (!isset($search_term)): ?>
            <p>Stay informed with our latest health tips, medical insights, and patient stories</p>
        <?php endif; ?>
    </div>
    <div class="scroll-hint">
        <span>Read our articles</span>
        <i class="fas fa-chevron-down"></i>
    </div>
</section>

<!-- Blog Section -->
<section class="blog-section" data-aos="fade-up">
    <div class="container">
        <div class="blog-grid">
            <!-- Main Content -->
            <div class="blog-main">
                <?php if (empty($posts)): ?>
                    <div class="no-posts">
                        <h2>No posts found</h2>
                        <?php if ($search_term): ?>
                            <p>No results found for '<?php echo htmlspecialchars($search_term); ?>'. Please try another search term.</p>
                        <?php else: ?>
                            <p>No blog posts have been published yet. Please check back soon!</p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <!-- Blog Posts Grid -->
                    <div class="blog-posts-grid">
                        <?php foreach ($posts as $post): ?>
                            <div class="blog-card" data-aos="fade-up" data-post-id="<?php echo $post['id']; ?>">                                <div class="blog-image">
                                    <!-- Enhanced image path handling for webserver compatibility -->
                                    <?php if (!empty($post['featured_image'])): ?>
                                        <a href="blog-post.php?slug=<?php echo $post['slug']; ?>">
                                            <img src="<?php echo htmlspecialchars($post['featured_image']); ?>"
                                                 alt="<?php echo htmlspecialchars($post['title']); ?>"
                                                 class="blog-thumbnail loaded"
                                                 onerror="this.parentElement.innerHTML='<div class=\'placeholder-image\'><i class=\'fas fa-newspaper\'></i></div>'; console.log('Image failed to load: <?php echo htmlspecialchars($post['featured_image']); ?>');">
                                        </a>
                                    <?php else: ?>
                                        <a href="blog-post.php?slug=<?php echo $post['slug']; ?>">
                                            <div class="placeholder-image">
                                                <i class="fas fa-newspaper"></i>
                                            </div>
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="blog-content">
                                    <div class="blog-meta">
                                        <span class="blog-date">
                                            <i class="fas fa-calendar"></i> <?php echo date('M j, Y', strtotime($post['published_at'])); ?>
                                        </span>
                                    </div>
                                    <h2><a href="blog-post.php?slug=<?php echo $post['slug']; ?>"><?php echo htmlspecialchars($post['title']); ?></a></h2>
                                    <p class="blog-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>

                                    <!-- Dynamic Content Preview -->
                                    <div class="content-preview">
                                        <p><?php echo htmlspecialchars(substr(strip_tags($post['content']), 0, 150)) . '...'; ?></p>
                                    </div>

                                    <!-- Social Media Sharing -->
                                    <div class="blog-social-share">
                                        <span class="share-label">Share:</span>
                                        <?php
                                        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                                        $full_url = $protocol . '://' . $_SERVER['HTTP_HOST'] . '/blog-post.php?slug=' . $post['slug'];
                                        $encoded_url = urlencode($full_url);
                                        $encoded_title = urlencode($post['title']);
                                        ?>
                                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo $encoded_url; ?>" target="_blank" rel="noopener" class="social-share facebook" title="Share on Facebook">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                        <a href="https://twitter.com/intent/tweet?url=<?php echo $encoded_url; ?>&text=<?php echo $encoded_title; ?>" target="_blank" rel="noopener" class="social-share twitter" title="Share on Twitter">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo $encoded_url; ?>" target="_blank" rel="noopener" class="social-share linkedin" title="Share on LinkedIn">
                                            <i class="fab fa-linkedin-in"></i>
                                        </a>
                                        <a href="https://api.whatsapp.com/send?text=<?php echo urlencode($post['title'] . ' - ' . $full_url); ?>" target="_blank" rel="noopener" class="social-share whatsapp" title="Share on WhatsApp">
                                            <i class="fab fa-whatsapp"></i>
                                        </a>
                                    </div>

                                    <a href="blog-post.php?slug=<?php echo $post['slug']; ?>" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-item">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            if ($start_page > 1) {
                                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => 1])) . '" class="pagination-item">1</a>';
                                if ($start_page > 2) {
                                    echo '<span class="pagination-ellipsis">...</span>';
                                }
                            }

                            for ($i = $start_page; $i <= $end_page; $i++) {
                                $active = $i == $page ? 'active' : '';
                                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $i])) . '" class="pagination-item ' . $active . '">' . $i . '</a>';
                            }

                            if ($end_page < $total_pages) {
                                if ($end_page < $total_pages - 1) {
                                    echo '<span class="pagination-ellipsis">...</span>';
                                }
                                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $total_pages])) . '" class="pagination-item">' . $total_pages . '</a>';
                            }
                            ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-item">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="blog-sidebar">
                <!-- Search Box -->
                <div class="sidebar-widget search-widget">
                    <form action="blog.php" method="GET" class="search-form">
                        <input type="text" name="search" placeholder="Search blog..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                        <button type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>

                <!-- Recent Posts -->
                <div class="sidebar-widget">
                    <h3>Recent Posts</h3>
                    <ul class="recent-posts">
                        <?php foreach ($recent_posts as $recent): ?>
                            <li>
                                <a href="blog-post.php?slug=<?php echo $recent['slug']; ?>">
                                    <!-- Enhanced image path handling for webserver compatibility -->                                    <?php if (!empty($recent['featured_image'])): ?>
                                        <div class="post-thumbnail">
                                            <img src="<?php echo htmlspecialchars($recent['featured_image']); ?>" alt="<?php echo htmlspecialchars($recent['title']); ?>" class="loaded">
                                        </div>
                                    <?php endif; ?>
                                    <div class="post-info">
                                        <h4><?php echo htmlspecialchars($recent['title']); ?></h4>
                                        <span class="post-date"><?php echo date('M j, Y', strtotime($recent['published_at'])); ?></span>
                                    </div>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="blog-cta" data-aos="fade-up">
    <div class="container">
        <div class="cta-content">
            <h2>Need Professional Healthcare Services?</h2>
            <p>Our team of qualified doctors and healthcare professionals is ready to provide personalized care at your doorstep.</p>
            <div class="cta-buttons">
                <a href="booking.php" class="btn btn-primary">Book an Appointment</a>
                <a href="contact.php" class="btn btn-outline">Contact Us</a>
            </div>
        </div>
    </div>
</section>

<!-- Dynamic Blog Thumbnails & Social Media Sharing JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic Card Animations
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = Math.random() * 0.3 + 's';
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1
    });

    document.querySelectorAll('.blog-card').forEach(card => {
        cardObserver.observe(card);
    });

    // Enhanced Hover Effects
    document.querySelectorAll('.blog-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.zIndex = '10';
        });

        card.addEventListener('mouseleave', function() {
            this.style.zIndex = '1';
        });
    });

    // Handle social media sharing clicks
    const socialLinks = document.querySelectorAll('.social-share');

    socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const platform = this.classList.contains('facebook') ? 'facebook' :
                           this.classList.contains('twitter') ? 'twitter' :
                           this.classList.contains('linkedin') ? 'linkedin' :
                           this.classList.contains('whatsapp') ? 'whatsapp' : 'other';

            // For desktop WhatsApp, open in popup
            if (platform === 'whatsapp' && window.innerWidth > 768) {
                e.preventDefault();
                const url = this.href.replace('api.whatsapp.com/send', 'web.whatsapp.com/send');
                window.open(url, 'whatsapp-share', 'width=600,height=600,scrollbars=yes,resizable=yes');
                return false;
            }

            // For other social platforms, open in popup
            if (platform !== 'whatsapp' && !this.classList.contains('email')) {
                e.preventDefault();
                const width = 600;
                const height = 400;
                const left = (window.innerWidth - width) / 2;
                const top = (window.innerHeight - height) / 2;

                window.open(
                    this.href,
                    'social-share',
                    `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
                );
                return false;
            }
        });
    });
});

// CSS Animation Classes
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.6s ease-out forwards;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>