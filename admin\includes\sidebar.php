<?php
// Get the current page filename to highlight the active menu item
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!-- Include Sidebar CSS -->
<link rel="stylesheet" href="css/sidebar.css">
<!-- Sidebar -->
<div class="sidebar">
    <div class="sidebar-header">
        <h1><img src="../images/logo.webp" alt="Doctors At Door Step" width="80px" height="80px" /></h1>
        <p>Admin Panel</p>
    </div>

    <div class="sidebar-menu">
        <a href="dashboard.php" class="menu-item <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </a>
        <a href="services.php" class="menu-item <?php echo ($current_page == 'services.php') ? 'active' : ''; ?>">
            <i class="fas fa-hand-holding-medical"></i> Services
        </a>        <a href="team.php" class="menu-item <?php echo ($current_page == 'team.php') ? 'active' : ''; ?>">
            <i class="fas fa-user-md"></i> Team Members
        </a>
        <a href="partners.php" class="menu-item <?php echo ($current_page == 'partners.php') ? 'active' : ''; ?>">
            <i class="fas fa-handshake"></i> Partners
        </a>
        <a href="testimonials.php" class="menu-item <?php echo ($current_page == 'testimonials.php') ? 'active' : ''; ?>">
            <i class="fas fa-quote-right"></i> Testimonials
        </a>
        <a href="blog-management.php" class="menu-item <?php echo ($current_page == 'blog-management.php' || $current_page == 'blog-edit.php') ? 'active' : ''; ?>">
            <i class="fas fa-blog"></i> Blog
        </a>
        <a href="bookings.php" class="menu-item <?php echo ($current_page == 'bookings.php') ? 'active' : ''; ?>">
            <i class="fas fa-calendar-check"></i> Bookings
        </a>
        <a href="pricing_plans.php" class="menu-item <?php echo ($current_page == 'pricing_plans.php') ? 'active' : ''; ?>">
            <i class="fas fa-tags"></i> Pricing Plans
        </a>
        <a href="faqs.php" class="menu-item <?php echo ($current_page == 'faqs.php') ? 'active' : ''; ?>">
            <i class="fas fa-question-circle"></i> FAQs
        </a>
        <a href="settings.php" class="menu-item <?php echo ($current_page == 'settings.php') ? 'active' : ''; ?>">
            <i class="fas fa-cog"></i> Settings
        </a>
        
    </div>

    <div class="sidebar-footer">
        <a href="dashboard.php?logout=true" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>

<!-- Fix for sidebar menu items -->
<style>
    /* Additional styles to ensure all menu items are clickable */
    .sidebar {
        height: 100%;
        overflow-y: hidden;
    }

    .sidebar-menu {
        padding-bottom: 70px; /* Add extra padding at the bottom to ensure last items are visible */
    }

    .sidebar-footer {
        position: fixed;
        bottom: 0;
        width: var(--sidebar-width);
        background-color: var(--dark-color);
        z-index: 1001;
    }
</style>
