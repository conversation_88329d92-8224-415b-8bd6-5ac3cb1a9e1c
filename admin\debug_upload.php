<?php
// Debug page for file upload issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>File Upload Debug Information</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .section{margin:20px 0;padding:15px;border:1px solid #ccc;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// 1. Check PHP configuration
echo "<div class='section'>";
echo "<h2>PHP Configuration</h2>";
echo "<strong>PHP Version:</strong> " . phpversion() . "<br>";
echo "<strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "<br>";
echo "<strong>Post Max Size:</strong> " . ini_get('post_max_size') . "<br>";
echo "<strong>Max File Uploads:</strong> " . ini_get('max_file_uploads') . "<br>";
echo "<strong>File Uploads Enabled:</strong> " . (ini_get('file_uploads') ? 'Yes' : 'No') . "<br>";
echo "<strong>Temp Directory:</strong> " . sys_get_temp_dir() . "<br>";
echo "</div>";

// 2. Check paths
echo "<div class='section'>";
echo "<h2>Path Information</h2>";
echo "<strong>Current Script Directory (__DIR__):</strong> " . __DIR__ . "<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Script Filename:</strong> " . $_SERVER['SCRIPT_FILENAME'] . "<br>";
echo "<strong>Project Root (dirname(__DIR__)):</strong> " . dirname(__DIR__) . "<br>";

// Test the upload directory path
$uploadDir1 = $_SERVER['DOCUMENT_ROOT'] . '/uploads/partners/';
$uploadDir2 = dirname(__DIR__) . '/uploads/partners/';
$uploadDir3 = __DIR__ . '/uploads/partners/';

echo "<br><strong>Upload Directory Options:</strong><br>";
echo "1. Using DOCUMENT_ROOT: " . $uploadDir1 . "<br>";
echo "2. Using dirname(__DIR__): " . $uploadDir2 . "<br>";
echo "3. Using __DIR__: " . $uploadDir3 . "<br>";
echo "</div>";

// 3. Check directory existence and permissions
echo "<div class='section'>";
echo "<h2>Directory Status</h2>";

$directories = [
    'DOCUMENT_ROOT method' => $uploadDir1,
    'dirname(__DIR__) method' => $uploadDir2,
    '__DIR__ method' => $uploadDir3,
    'uploads (relative)' => 'uploads/',
    'uploads/partners (relative)' => 'uploads/partners/'
];

foreach ($directories as $name => $dir) {
    echo "<strong>$name ($dir):</strong><br>";
    
    if (file_exists($dir)) {
        echo "&nbsp;&nbsp;<span class='success'>✓ Directory exists</span><br>";
        
        if (is_dir($dir)) {
            echo "&nbsp;&nbsp;<span class='success'>✓ Is a directory</span><br>";
        } else {
            echo "&nbsp;&nbsp;<span class='error'>✗ Not a directory</span><br>";
        }
        
        if (is_writable($dir)) {
            echo "&nbsp;&nbsp;<span class='success'>✓ Directory is writable</span><br>";
        } else {
            echo "&nbsp;&nbsp;<span class='error'>✗ Directory is not writable</span><br>";
        }
        
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "&nbsp;&nbsp;<span class='info'>Permissions: $perms</span><br>";
    } else {
        echo "&nbsp;&nbsp;<span class='error'>✗ Directory does not exist</span><br>";
        
        // Try to create it
        echo "&nbsp;&nbsp;Attempting to create directory...<br>";
        if (@mkdir($dir, 0755, true)) {
            echo "&nbsp;&nbsp;<span class='success'>✓ Directory created successfully</span><br>";
        } else {
            echo "&nbsp;&nbsp;<span class='error'>✗ Failed to create directory</span><br>";
        }
    }
    echo "<br>";
}
echo "</div>";

// 4. Test file upload if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<div class='section'>";
    echo "<h2>File Upload Test Results</h2>";
    
    $file = $_FILES['test_file'];
    echo "<strong>File Info:</strong><br>";
    echo "Name: " . $file['name'] . "<br>";
    echo "Type: " . $file['type'] . "<br>";
    echo "Size: " . $file['size'] . " bytes<br>";
    echo "Temp Name: " . $file['tmp_name'] . "<br>";
    echo "Error Code: " . $file['error'] . "<br>";
    
    if ($file['error'] === 0) {
        echo "<span class='success'>✓ File uploaded to temp location successfully</span><br>";
        
        // Test different upload directories
        $testDirs = [
            'uploads/partners/' => 'uploads/partners/',
            'dirname method' => dirname(__DIR__) . '/uploads/partners/',
            'document_root method' => $_SERVER['DOCUMENT_ROOT'] . '/uploads/partners/'
        ];
        
        foreach ($testDirs as $name => $testDir) {
            echo "<br><strong>Testing $name ($testDir):</strong><br>";
            
            // Create directory if needed
            if (!is_dir($testDir)) {
                if (@mkdir($testDir, 0755, true)) {
                    echo "&nbsp;&nbsp;<span class='success'>✓ Created directory</span><br>";
                } else {
                    echo "&nbsp;&nbsp;<span class='error'>✗ Failed to create directory</span><br>";
                    continue;
                }
            }
            
            $filename = 'test_' . time() . '_' . $file['name'];
            $targetPath = $testDir . $filename;
            
            if (move_uploaded_file($file['tmp_name'], $targetPath)) {
                echo "&nbsp;&nbsp;<span class='success'>✓ File moved successfully to: $targetPath</span><br>";
                
                // Clean up test file
                if (file_exists($targetPath)) {
                    unlink($targetPath);
                    echo "&nbsp;&nbsp;<span class='info'>Test file cleaned up</span><br>";
                }
                break; // Stop after first successful upload
            } else {
                echo "&nbsp;&nbsp;<span class='error'>✗ Failed to move file to: $targetPath</span><br>";
            }
        }
    } else {
        echo "<span class='error'>✗ File upload error: ";
        switch ($file['error']) {
            case UPLOAD_ERR_INI_SIZE:
                echo "File too large (exceeds upload_max_filesize)";
                break;
            case UPLOAD_ERR_FORM_SIZE:
                echo "File too large (exceeds MAX_FILE_SIZE)";
                break;
            case UPLOAD_ERR_PARTIAL:
                echo "File only partially uploaded";
                break;
            case UPLOAD_ERR_NO_FILE:
                echo "No file uploaded";
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                echo "Missing temporary folder";
                break;
            case UPLOAD_ERR_CANT_WRITE:
                echo "Failed to write file to disk";
                break;
            case UPLOAD_ERR_EXTENSION:
                echo "File upload stopped by extension";
                break;
            default:
                echo "Unknown error";
        }
        echo "</span><br>";
    }
    echo "</div>";
}

// 5. Test form
echo "<div class='section'>";
echo "<h2>Test File Upload</h2>";
echo "<form method='POST' enctype='multipart/form-data'>";
echo "<input type='file' name='test_file' accept='image/*' required><br><br>";
echo "<button type='submit'>Test Upload</button>";
echo "</form>";
echo "</div>";

// 6. Server information
echo "<div class='section'>";
echo "<h2>Server Information</h2>";
echo "<strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "<strong>Server Name:</strong> " . $_SERVER['SERVER_NAME'] . "<br>";
echo "<strong>HTTP Host:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";
echo "</div>";
?>
