# Admin directory - all pages must use .php extension
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /admin/
    
    # Force ALL admin pages to use .php extension
    # Redirect any request without .php to the .php version
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}.php -f
    RewriteRule ^([^.]+)$ $1.php [R=301,L]
</IfModule>

# Allow all access for debugging
<RequireAll>
    Require all granted
</RequireAll>
