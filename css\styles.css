/* Global Variables - Beautiful Red & Blue Color Scheme */
:root {
    /* Primary Red Palette - Sophisticated Crimson */
    --primary-color: #DC2626;        /* Modern crimson red */
    --primary-dark: #B91C1C;         /* Deep crimson */
    --primary-light: #F87171;        /* Soft coral red */
    --primary-ultra-light: #FEF2F2;  /* Very light red background */
    
    /* Secondary Blue Palette - Professional Navy to Sky */
    --secondary-color: #1E40AF;      /* Professional navy blue */
    --secondary-dark: #1E3A8A;       /* Deep navy */
    --secondary-light: #3B82F6;      /* Bright blue */
    --secondary-ultra-light: #EFF6FF; /* Very light blue background */
    
    /* Accent Colors - Sophisticated Grays */
    --accent-color: #64748B;         /* Slate gray for text */
    --accent-light: #94A3B8;         /* Light slate */
    --accent-dark: #475569;          /* Dark slate */
    
    /* Tertiary Colors - Elegant Complement */
    --tertiary-color: #059669;       /* Emerald green */
    --tertiary-light: #10B981;       /* Light emerald */
    --tertiary-dark: #047857;        /* Deep emerald */
    
    /* Neutral Colors */
    --dark-color: #1E293B;           /* Rich dark blue-gray */
    --light-color: #F8FAFC;          /* Clean light gray */
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* Status Colors */
    --success-color: #059669;
    --warning-color: #D97706;
    --error-color: #DC2626;
    --info-color: #2563EB;
    --white: #FFFFFF;
    
    /* Enhanced Shadows */
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-red: 0 4px 14px 0 rgba(220, 38, 38, 0.25);
    --box-shadow-red-hover: 0 8px 25px 0 rgba(220, 38, 38, 0.35);
    --box-shadow-blue: 0 4px 14px 0 rgba(30, 64, 175, 0.25);
    --box-shadow-blue-hover: 0 8px 25px 0 rgba(30, 64, 175, 0.35);
    --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Design System */
    --card-radius: 16px;
    --button-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --section-spacing: 100px;
    
    /* Gradients */
    --gradient-red: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-blue: linear-gradient(135deg, var(--secondary-light) 0%, var(--secondary-color) 100%);
    --gradient-red-blue: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-blue-red: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
}

/* Services Grid Section - Enhanced & Optimized */
.services-grid-section {
    padding: var(--section-spacing) 0;
    background: var(--white);
    position: relative;
}

.services-grid-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, var(--gray-100) 1px, transparent 1px),
                linear-gradient(to bottom, var(--gray-100) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.5;
    pointer-events: none;
}

/* Services Horizontal Grid */
.services-horizontal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card-horizontal {
    display: flex;
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    height: 100%;
}

.service-card-horizontal:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
    border-color: transparent;
}

.service-card-horizontal:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 5px;
    background: var(--primary-color);
    transform: scaleY(0);
    transform-origin: top;
    transition: transform 0.3s ease;
}

.service-card-horizontal:hover:before {
    transform: scaleY(1);
}

.service-image {
    position: relative;
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    aspect-ratio: 16/9;
}

@supports not (aspect-ratio: 16/9) {
    .service-image::before {
        content: "";
        display: block;
        padding-top: 56.25%;
    }
}

.service-item:hover .service-image {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.service-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.2) 0%, rgba(30, 64, 175, 0.1) 70%, transparent 100%);
    z-index: 1;
    opacity: 0;
    transition: var(--transition);
}

.service-item:hover .service-image::before {
    opacity: 1;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
    position: absolute;
    top: 0;
    left: 0;
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-font-smoothing: subpixel-antialiased;
}

@media (prefers-reduced-motion: reduce) {
    .service-image img,
    .service-item,
    .service-image,
    .service-details h2::after {
        transition: none;
    }
}

/* Add loading state for images */
.service-image.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--gray-200) 0%, var(--gray-100) 50%, var(--gray-200) 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.service-card-icon {
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    background: var(--primary-ultra-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--primary-color);
    transition: var(--transition);
    border: 2px solid transparent;
}

.service-card-horizontal:hover .service-card-icon {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-light);
    transform: scale(1.05);
}

.service-card-content {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

.service-card-content h3 {
    margin: 0 0 1rem;
    color: var(--dark-color);
    font-size: 1.5rem;
    transition: var(--transition);
}

.service-card-horizontal:hover .service-card-content h3 {
    color: var(--primary-color);
}

.service-card-content p {
    color: var(--accent-color);
    font-size: 1.05rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.service-details ul {
    list-style: none;
    margin: 1.2rem 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.service-details ul li {
    position: relative;
    padding-left: 1.8rem;
    margin-bottom: 0.8rem;
    color: var(--accent-color);
    font-size: clamp(0.85rem, 1.5vw, 1rem);
}

.service-details ul li::before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-size: 0.9rem;
    top: 50%;
    transform: translateY(-50%);
}

.service-card-meta {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.service-price, .service-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.95rem;
    background: var(--gray-50);
    padding: 0.5rem 1rem;
    border-radius: 30px;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.service-card-horizontal:hover .service-price,
.service-card-horizontal:hover .service-duration {
    background: var(--primary-ultra-light);
    border-color: var(--primary-light);
    color: var(--primary-dark);
}

.service-price::before {
    content: '\f0d6';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.service-duration::before {
    content: '\f017';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.service-card-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
    padding: 0.5rem 0;
    transition: var(--transition);
    position: relative;
    width: fit-content;
}

.service-card-link:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.service-card-link:hover:after {
    width: 100%;
}

.service-card-link i {
    transition: var(--transition);
}

.service-card-link:hover i {
    transform: translateX(5px);
}

/* Service Process - Enhanced */
.service-process {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
    position: relative;
}

.service-process:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.process-steps {
    max-width: 900px;
    margin: 4rem auto 0;
    position: relative;
    z-index: 2;
}

.process-steps:before {
    content: '';
    position: absolute;
    top: 0;
    left: 40px;
    height: 100%;
    width: 3px;
    background: var(--gray-300);
    border-radius: 3px;
}

.process-step {
    display: flex;
    align-items: flex-start;
    gap: 2.5rem;
    margin-bottom: 4rem;
    position: relative;
}

.process-step:last-child {
    margin-bottom: 0;
}

.step-circle {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(44, 123, 229, 0.3);
    position: relative;
}

.step-content {
    flex: 1;
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.process-step:hover .step-content {
    transform: translateX(10px);
    box-shadow: var(--box-shadow-hover);
}

.step-content h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.step-content p {
    color: var(--secondary-color);
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 0;
}

/* FAQ Section - Enhanced */
.faq-section {
    padding: var(--section-spacing) 0;
    background: var(--white);
}

.faq-accordion {
    max-width: 900px;
    margin: 3rem auto 0;
}

.faq-item {
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--card-radius);
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover, .faq-item.active {
    box-shadow: var(--box-shadow);
    border-color: transparent;
}

.faq-question {
    padding: 1.8rem;
    background: var(--white);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
}

.faq-item.active .faq-question {
    background: linear-gradient(to right, var(--primary-ultra-light), var(--secondary-ultra-light));
    border-color: var(--primary-light);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.15rem;
    font-weight: 600;
    line-height: 1.4;
    transition: var(--transition);
}

.faq-item.active .faq-question h3 {
    color: var(--primary-color);
}

.faq-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    min-height: 24px;
    border-radius: 50%;
    background: var(--gray-200);
    color: var(--dark-color);
    transition: var(--transition);
    font-size: 0.8rem;
}

.faq-item.active .faq-toggle, .faq-item:hover .faq-toggle {
    background: var(--primary-color);
    color: var(--white);
}

.faq-item.active .faq-toggle i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 1.8rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease-out;
}

.faq-item.active .faq-answer {
    padding: 0 1.8rem 1.8rem;
    max-height: 300px;
}

/* Booking CTA - Enhanced */
.booking-cta {
    padding: 100px 0;
    background: linear-gradient(rgba(26, 43, 60, 0.85), rgba(26, 43, 60, 0.85)),
                url('../images/booking-cta-bg.jpg') center/cover;
    color: var(--white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.booking-cta:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 5px;
    top: 0;
    left: 0;
    background: var(--gradient-red-blue);
}

.booking-cta .cta-content {
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.booking-cta h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--white);
}

.booking-cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Service Details Page - Enhanced */
.service-details-section {
    padding: var(--section-spacing) 0;
    background: var(--white);
}

.service-details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

.service-main-content h2 {
    margin-bottom: 2rem;
    color: var(--dark-color);
    position: relative;
    padding-bottom: 15px;
}

.service-main-content h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.service-features,
.service-benefits {
    list-style: none;
    margin-bottom: 3rem;
}

.service-features li,
.service-benefits li {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1.8rem;
    background: var(--light-color);
    border-radius: var(--card-radius);
    transition: var(--transition);
}

.service-features li:hover,
.service-benefits li:hover {
    transform: translateX(10px);
    box-shadow: var(--box-shadow);
    background: var(--white);
}

.service-features i,
.service-benefits i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 0.2rem;
}

/* Service Sidebar */
.service-sidebar {
    position: sticky;
    top: 100px;
}

.pricing-box {
    padding: 2.5rem;
    background: var(--light-color);
    border-radius: var(--card-radius);
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.pricing-box h3 {
    margin-bottom: 1.5rem;
    color: var(--dark-color);
    font-size: 1.5rem;
    position: relative;
    padding-bottom: 15px;
}

.pricing-box h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.price-details {
    margin: 1.5rem 0;
    background-color: var(--white);
    border-radius: var(--card-radius);
    padding: 1.5rem;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
    border-bottom: 1px solid var(--gray-200);
}

.price-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.price-item .label {
    color: var(--secondary-color);
    font-weight: 500;
}

.price-item .value {
    font-weight: 700;
    color: var(--primary-color);
}

.pricing-note {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin: 1.2rem 0 1.5rem;
    font-style: italic;
}

.contact-box {
    padding: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border-radius: var(--card-radius);
    text-align: center;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.contact-box:before {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    top: -100px;
    right: -100px;
}

.contact-box h3 {
    margin-bottom: 1.2rem;
    color: var(--white);
    font-size: 1.5rem;
}

.contact-box p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.phone-link {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    color: var(--white);
    background: rgba(255, 255, 255, 0.2);
    padding: 0.8rem 1.5rem;
    border-radius: var(--button-radius);
    font-size: 1.1rem;
    font-weight: 600;
    transition: var(--transition);
}

.phone-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
}

.phone-link i {
    font-size: 1.2rem;
}

/* Service Testimonials */
.service-testimonials {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
}

.service-testimonials h2 {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

.service-testimonials h2:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

/* Contact Page Styles - Enhanced */

/* Contact Section */
.contact-section {
    padding: var(--section-spacing) 0;
    background: var(--white);
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

/* Form and Map Grid Layout */
.form-map-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.contact-map-wrapper {
    height: 100%;
    min-height: 350px;
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

/* Contact Form */
.contact-form-wrapper h2 {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 15px;
}

.contact-form-wrapper h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.contact-form-wrapper p {
    color: var(--secondary-color);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.contact-form {
    background: var(--light-color);
    padding: 2.5rem;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 1.8rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    color: var(--dark-color);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    background: var(--white);
    transition: var(--transition);
    font-family: inherit;
    color: var(--dark-color);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 4px rgba(44, 123, 229, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--secondary-color);
    opacity: 0.7;
}

/* Contact Information */
.contact-info-wrapper {
    height: 100%;
}

.contact-info-box {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 2.5rem;
    border-radius: var(--card-radius);
    height: 100%;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.contact-info-box:before,
.contact-info-box:after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.contact-info-box:before {
    top: -100px;
    right: -100px;
}

.contact-info-box:after {
    bottom: -100px;
    left: -100px;
}

.contact-info-box h2 {
    color: var(--white);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 15px;
}

.contact-info-box h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--white);
    border-radius: 2px;
}

.contact-info-box p {
    opacity: 0.9;
    margin-bottom: 2rem;
}

.contact-details {
    margin-bottom: 2.5rem;
    position: relative;
    z-index: 2;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.2rem;
    margin-bottom: 2rem;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item i {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.contact-text h3 {
    color: var(--white);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.contact-text p {
    margin-bottom: 0.25rem;
    opacity: 0.9;
}

.text-muted {
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Social Links */
.contact-social {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 2rem;
    position: relative;
    z-index: 2;
}

.contact-social h3 {
    color: var(--white);
    margin-bottom: 1.2rem;
    font-size: 1.2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    font-size: 1.2rem;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-5px);
}

/* Map Section */
.map-section {
    padding: 0;
}

.map-wrapper {
    height: 500px;
    width: 100%;
    position: relative;
    box-shadow: var(--box-shadow);
}

.map-wrapper iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Emergency Contact */
.emergency-contact {
    padding: 80px 0;
    background: linear-gradient(rgba(26, 43, 60, 0.9), rgba(26, 43, 60, 0.9)),
                url('../images/emergency-contact-bg.jpg') center/cover;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.emergency-contact:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.emergency-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    text-align: center;
    position: relative;
    z-index: 2;
}

.emergency-icon {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    font-size: 2.5rem;
    color: var(--white);
    position: relative;
}

.emergency-icon:before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: rotate 30s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.emergency-text h2 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.emergency-text p {
    opacity: 0.9;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.emergency-phone {
    display: block;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--white);
    margin-top: 1.5rem;
    transition: var(--transition);
}

.emergency-phone:hover {
    color: var(--primary-light);
    transform: scale(1.05);
}

/* Booking Form - Simplified */
.booking-section {
    padding: 4rem 0;
    background: var(--light-color);
}

.booking-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.booking-form-wrapper {
    position: relative;
}

.booking-form-wrapper h2 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-size: 1.8rem;
}

.booking-form-wrapper p {
    margin-bottom: 2rem;
    color: var(--secondary-color);
}

.form-content {
    background: var(--white);
    border-radius: var(--card-radius);
    padding: 2.5rem;
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--button-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 123, 229, 0.1);
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
    margin-top: 0.2rem;
}

.checkbox-group label {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Booking Info */
.booking-info-box {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 2.5rem;
    border-radius: var(--card-radius);
    position: sticky;
    top: 100px;
    box-shadow: var(--box-shadow);
}

.booking-info-box h2 {
    color: var(--white);
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 15px;
}

.booking-info-box h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--white);
    border-radius: 2px;
}

.info-section {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.info-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.info-section h3 {
    color: var(--white);
    margin-bottom: 1.2rem;
    font-size: 1.3rem;
}

.info-section ul {
    list-style: none;
}

.info-section ul li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.info-section ul li:before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

[data-aos="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: transform 0.8s ease, opacity 0.8s ease;
}

[data-aos="fade-left"] {
    opacity: 0;
    transform: translateX(-30px);
    transition: transform 0.8s ease, opacity 0.8s ease;
}

[data-aos="fade-right"] {
    opacity: 0;
    transform: translateX(30px);
    transition: transform 0.8s ease, opacity 0.8s ease;
}

[data-aos="fade"] {
    opacity: 0;
    transition: opacity 0.8s ease;
}

/* Media Queries */
@media (max-width: 1200px) {
    h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2.2rem;
    }

    .container {
        padding: 0 30px;
    }

    .hero-content h1 {
        font-size: 3.8rem;
    }

    .service-details-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .service-sidebar {
        position: static;
        max-width: 600px;
        margin: 0 auto;
    }

    .story-grid {
        gap: 3rem;
    }
}

@media (max-width: 992px) {
    :root {
        --section-spacing: 80px;
    }

    h1 {
        font-size: 2.8rem;
    }

    h2 {
        font-size: 2rem;
    }

    .section-header {
        margin-bottom: 40px;
    }

    .story-grid,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    /* Show mobile image and hide desktop image on mobile devices */
    .mobile-story-image {
        display: block !important;
    }
    
    .desktop-story-image {
        display: none !important;
    }
    
    .story-content {
        grid-column: 1;
    }
    
    .story-text {
        margin-top: 0;
    }

    .form-map-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-map-wrapper {
        min-height: 300px;
    }

    /* Responsive service cards */
    .services-horizontal-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }

    .booking-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }

    .booking-info-box {
        position: static;
        margin-top: 2rem;
    }

    .emergency-content {
        flex-direction: column;
        gap: 2rem;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 2rem 0;
        z-index: 1000;
        overflow-y: auto;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
        box-shadow: var(--box-shadow);
    }

    .nav-link {
        padding: 1rem 2rem;
        width: 100%;
        text-align: center;
    }

    .nav-link:after {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }
}

@media (max-width: 768px) {
    :root {
        --section-spacing: 60px;
    }

    body {
        font-size: 15px;
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    /* Responsive service cards for tablets */
    .services-horizontal-grid {
        grid-template-columns: 1fr;
    }

    .service-card-horizontal {
        flex-direction: row;
    }

    .hero {
        padding: 130px 0 60px;
    }

    .hero-content h1 {
        font-size: 2.8rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-cta .btn {
        width: 100%;
    }



    .hero-slider-dots .swiper-pagination-bullet {
        width: 10px !important;
        height: 10px !important;
    }

    .service-item {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 2.5rem 0;
    }

    .service-item:nth-child(even) {
        direction: ltr;
    }

    .stats-number {
        font-size: 2.8rem;
    }

    .testimonial-card {
        padding: 2rem;
    }

    .cta-content h2,
    .booking-cta h2,
    .emergency-text h2 {
        font-size: 2.2rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        text-align: center;
    }

    .footer-section h3:after {
        left: 50%;
        transform: translateX(-50%);
    }

    .contact-info li {
        justify-content: center;
    }

    .social-icons,
    .newsletter-form {
        justify-content: center;
    }

    .approach-step {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
        text-align: center;
    }

    .approach-steps:before {
        display: none;
    }

    .process-step {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
        text-align: center;
    }

    .process-steps:before {
        display: none;
    }

    .step-content h3,
    .step-content p {
        text-align: center;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 15px;
    }

    h1 {
        font-size: 2.2rem;
    }

    h2 {
        font-size: 1.6rem;
    }

    h3 {
        font-size: 1.3rem;
    }

    .contact-map-wrapper {
        min-height: 250px;
    }

    /* Smaller screen service cards */
    .service-card-horizontal {
        flex-direction: column;
    }

    .service-card-icon {
        width: 100%;
        height: 100px;
    }

    .service-card-content {
        padding: 1.5rem;
    }

    .service-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }



    .hero-slider-dots .swiper-pagination-bullet {
        width: 8px !important;
        height: 8px !important;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        width: 100%;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-buttons .btn {
        width: 100%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .service-features li,
    .service-benefits li,
    .value-item,
    .mission-box {
        padding: 1.5rem;
    }

    .emergency-phone {
        font-size: 1.8rem;
    }

    .mission-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.7;
    color: var(--dark-color);
    background: var(--white);
    font-size: 16px;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--dark-color);
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1.2rem;
}

h3 {
    font-size: 1.8rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.2rem;
}

p {
    margin-bottom: 1.5rem;
    font-weight: 400;
}

a {
    color: inherit;
    text-decoration: none;
    transition: var(--transition);
}

strong, b {
    font-weight: 600;
}

/* Section Styles */
section {
    padding: var(--section-spacing) 0;
    position: relative;
    overflow: hidden;
}

.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 60px;
    position: relative;
}

.section-header h2 {
    position: relative;
    display: inline-block;
}

.section-header h2:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -15px;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.section-header p {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-top: 25px;
}

/* Header & Navigation - Enhanced */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: var(--white);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    transition: var(--transition);
    height: 80px;
}

.header.scroll-down {
    transform: translateY(-100%);
}

.header.scroll-up {
    transform: translateY(0);
    box-shadow: var(--box-shadow);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo {
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.logo h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin: 0;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.logo:hover {
    transform: translateY(-2px);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: var(--transition);
    position: relative;
}

.nav-link:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link:hover:after,
.nav-link.active:after {
    width: 70%;
}

.nav-link.active {
    color: var(--primary-color);
}

.cta-button {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.75rem 1.75rem;
    border-radius: var(--button-radius);
    font-weight: 600;
    box-shadow: var(--box-shadow-red);
}

.cta-button:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-red-hover);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 6px;
    cursor: pointer;
    z-index: 1001;
}

.nav-toggle span {
    display: block;
    width: 28px;
    height: 2px;
    background: var(--dark-color);
    transition: var(--transition);
}

/* Footer Styles - Enhanced */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 5rem 0 2rem;
    position: relative;
}

.footer:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-red-blue);
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    position: relative;
    padding-bottom: 10px;
}

.footer-section h3:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 3px;
    background: var(--primary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 1rem;
    transition: var(--transition);
}

.footer-section a {
    color: var(--gray-300);
    position: relative;
    transition: var(--transition);
    display: inline-block;
}

.footer-section a:hover {
    color: var(--white);
    transform: translateX(5px);
}

.contact-info li {
    display: flex;
    align-items: start;
    gap: 15px;
    margin-bottom: 1.2rem;
}

.contact-info li i {
    color: var(--primary-light);
    font-size: 1.2rem;
    margin-top: 3px;
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: var(--button-radius);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: 0.95rem;
}

.newsletter-form input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
}

.newsletter-form input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.newsletter-form button {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.newsletter-form button:hover {
    background: var(--primary-light);
    transform: translateY(-3px);
}

.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition);
    font-size: 1rem;
}

.social-icon:hover {
    background: var(--primary-color);
    transform: translateY(-5px) rotate(10deg);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--gray-300);
    font-size: 0.9rem;
}

/* Button Styles - Enhanced */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: var(--button-radius);
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    z-index: -1;
}

.btn:hover:before {
    width: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: var(--box-shadow-red);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-red-hover);
}

.btn-outline {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-red);
}

.btn-accent {
    background: var(--tertiary-color);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(54, 180, 167, 0.3);
}

.btn-accent:hover {
    background: var(--tertiary-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(54, 180, 167, 0.4);
}

/* New Blue Button Variant */
.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
    box-shadow: var(--box-shadow-blue);
}

.btn-secondary:hover {
    background: var(--secondary-dark);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-blue-hover);
}

.btn-outline-secondary {
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-blue);
}

/* Hero Section - Beautiful Red & Blue Background */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 140px 0 80px;
    overflow: hidden;
    background: linear-gradient(135deg, 
        #1E293B 0%, 
        #1E40AF 25%, 
        #DC2626 75%, 
        #B91C1C 100%);
    will-change: transform;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 64, 175, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(248, 113, 113, 0.2) 0%, transparent 50%);
    z-index: 1;
    animation: gradientShift 10s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Hero Background Slider */
.hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-swiper {
    width: 100%;
    height: 100%;
}

.hero-slide {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    transition: transform 0.6s ease;
}

.hero-slide .slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 123, 229, 0.6), rgba(26, 43, 60, 0.7));
    z-index: 2;
    transition: opacity 0.6s ease;
}

/* Slide Content */
.slide-content {
    position: absolute;
    top: 50%;
    right: 50px;
    transform: translateY(-50%);
    z-index: 3;
    opacity: 0.8;
}

.slide-badge {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 12px 20px;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInRight 1s ease-out;
}

.slide-badge i {
    font-size: 1.2rem;
    color: var(--accent-color);
}

/* Animated Background Elements */
.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}



.hero-content {
    position: relative;
    z-index: 10;
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
    color: var(--white);
    text-align: center;
}

.hero-content-inner {
    position: relative;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px 20px;
    border-radius: 50px;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInDown 1s ease-out;
}

.hero-badge i {
    color: var(--accent-color);
}



/* Enhanced Dots */
.hero-slider-dots {
    position: absolute !important;
    bottom: 30px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    display: flex !important;
    gap: 10px;
    justify-content: center !important;
    align-items: center !important;
    z-index: 10;
    width: auto !important;
    height: auto !important;
}

.hero-slider-dots .swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    border-radius: 50% !important;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin: 0 5px !important;
    opacity: 1 !important;
}

.hero-slider-dots .swiper-pagination-bullet-active {
    background: var(--white) !important;
    border-color: var(--accent-color);
    transform: scale(1.2);
}

.hero-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    z-index: 10;
}

.hero-prev {
    left: 30px;
}

.hero-next {
    right: 30px;
}

.hero-arrow:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
    border-color: var(--accent-color);
}

.hero-content h1 {
    font-size: 4.8rem;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    color: var(--white);
    font-weight: 800;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    animation: fadeInUp 1s ease-out 0.4s both;
}



.hero-content p {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.hero .btn-enhanced {
    padding: 1.3rem 2.8rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.hero .btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.hero .btn-enhanced:hover::before {
    left: 100%;
}

.hero .btn-primary.btn-enhanced {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    box-shadow: 0 8px 25px rgba(44, 123, 229, 0.3);
}

.hero .btn-primary.btn-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(44, 123, 229, 0.4);
}

.hero .btn-outline.btn-enhanced {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: var(--white);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.hero .btn-outline.btn-enhanced:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
}

/* Trust Indicators */
.hero-trust-indicators {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    animation: fadeInUp 1s ease-out 1s both;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    font-weight: 500;
}

.trust-item i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

/* Scroll Indicator */


/* Additional Visual Enhancements */
.hero-slide {
    transform: scale(1.05);
    transition: transform 8s ease-out;
}

.hero-slide.swiper-slide-active {
    transform: scale(1);
}

/* Glassmorphism effects */
.hero-badge,
.slide-badge,
.hero .btn-outline.btn-enhanced,
.hero-arrow {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

/* Enhanced hover effects */
.hero .btn-enhanced:hover {
    transform: translateY(-3px) scale(1.02);
}

.trust-item:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.trust-item:hover i {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Improved text readability */
.hero-content h1,
.hero-content p {
    text-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);
}

/* Loading animation for hero */
.hero-content-inner {
    opacity: 0;
    animation: heroContentLoad 2s ease-out 0.5s forwards;
}

@keyframes heroContentLoad {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Hero Responsive Styles */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 3.2rem;
    }

    .hero-content p {
        font-size: 1.2rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .hero .btn-enhanced {
        padding: 1.1rem 2.2rem;
        font-size: 1rem;
    }

    .hero-trust-indicators {
        flex-direction: column;
        gap: 1rem;
    }

    .slide-content {
        right: 20px;
    }

    .hero-arrow {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .hero-prev {
        left: 15px;
    }

    .hero-next {
        right: 15px;
    }

    .floating-element {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
        padding: 0 10px;
    }

    .hero .btn-enhanced {
        padding: 1rem 2rem;
        font-size: 0.95rem;
    }


}

/* Page Hero - Enhanced for internal pages */
.page-hero {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 160px 0 100px;
    background: linear-gradient(135deg, rgba(44, 123, 229, 0.9) 0%, rgba(18, 103, 117, 0.9) 100%), url(../images/about-hero.jpg) center / cover;
    color: var(--white);
    margin-bottom: 0;
    overflow: hidden;
}

/* Enhanced overlay with animated particles */
.page-hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
    z-index: 1;
}

/* Smooth bottom transition */
.page-hero:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 80px;
    background: linear-gradient(to top, var(--white) 0%, transparent 100%);
    z-index: 2;
}

/* Enhanced container with subtle animation */
.page-hero .container {
    position: relative;
    z-index: 3;
    animation: fadeInUp 1s ease-out;
}

.page-hero h1 {
    font-size: 3.8rem;
    margin-bottom: 1.5rem;
    color: var(--white);
    position: relative;
    z-index: 3;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    font-weight: 700;
    line-height: 1.2;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.page-hero p {
    font-size: 1.3rem;
    max-width: 750px;
    margin: 0 auto;
    opacity: 0.95;
    position: relative;
    z-index: 3;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.4s both;
}

/* Page-specific hero backgrounds */
.about-hero {
    background-image: linear-gradient(135deg, rgba(44, 123, 229, 0.9) 0%, rgba(18, 103, 117, 0.9) 100%),
                      url('https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

.services-hero {
    background-image: linear-gradient(135deg, rgba(26, 43, 60, 0.85) 0%, rgba(44, 123, 229, 0.85) 100%),
                      url('https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

.booking-hero {
    background-image: linear-gradient(135deg, rgba(54, 180, 167, 0.9) 0%, rgba(44, 123, 229, 0.9) 100%),
                      url('https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

.contact-hero {
    background-image: linear-gradient(135deg, rgba(44, 123, 229, 0.9) 0%, rgba(26, 43, 60, 0.9) 100%),
                      url('https://images.unsplash.com/photo-1516574187841-cb9cc2ca948b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

.pricing-hero {
    background-image: linear-gradient(135deg, rgba(18, 103, 117, 0.9) 0%, rgba(54, 180, 167, 0.9) 100%),
                      url('https://images.unsplash.com/photo-1551601651-2a8555f1a136?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

.blog-hero {
    background-image: linear-gradient(135deg, rgba(44, 123, 229, 0.9) 0%, rgba(26, 43, 60, 0.9) 100%),
                      url('https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

.blog-post-hero {
    background-image: linear-gradient(135deg, rgba(26, 43, 60, 0.85) 0%, rgba(44, 123, 229, 0.85) 100%),
                      url('https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
}

/* Enhanced hero animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(1deg);
    }
    66% {
        transform: translateY(5px) rotate(-1deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero responsive design */
@media (max-width: 768px) {
    .page-hero {
        min-height: 50vh;
        padding: 120px 0 80px;
    }

    .page-hero h1 {
        font-size: 2.8rem;
        margin-bottom: 1rem;
    }

    .page-hero p {
        font-size: 1.1rem;
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .page-hero {
        min-height: 45vh;
        padding: 100px 0 60px;
    }

    .page-hero h1 {
        font-size: 2.2rem;
        line-height: 1.3;
    }

    .page-hero p {
        font-size: 1rem;
        padding: 0 15px;
    }
}

/* Hero breadcrumb styling */
.page-hero .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 8px 20px;
    margin-bottom: 20px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    animation: fadeInUp 1s ease-out 0.1s both;
}

.page-hero .breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.page-hero .breadcrumb a:hover {
    color: var(--white);
}

.page-hero .breadcrumb .separator {
    color: rgba(255, 255, 255, 0.6);
}

/* Hero scroll indicator */
.page-hero .scroll-hint {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: center;
    animation: bounce 2s infinite;
    z-index: 3;
}

.page-hero .scroll-hint i {
    display: block;
    font-size: 1.2rem;
    margin-top: 5px;
}

/* Enhanced hover effects for hero content */
.page-hero .container:hover h1 {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

.page-hero .container:hover p {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Subtle parallax effect */
@media (min-width: 769px) {
    .page-hero {
        background-attachment: fixed;
    }
}

/* Services Overview - Enhanced */
.services-overview {
    padding: var(--section-spacing) 0;
    background: var(--white);
    position: relative;
}

.services-overview:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(var(--gray-200) 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.3;
    z-index: 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    padding: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.service-card {
    padding: 2.5rem 2rem;
    background: var(--white);
    border-radius: var(--card-radius);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.service-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-color);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--box-shadow-hover);
}

.service-card:hover:before {
    height: 10px;
}

.service-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(44, 123, 229, 0.1);
    color: var(--primary-color);
    border-radius: 50%;
    font-size: 2.5rem;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    background: var(--primary-color);
    color: var(--white);
    transform: rotateY(180deg);
}

.service-card h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-size: 1.5rem;
    font-weight: 700;
    transition: var(--transition);
}

.service-card:hover h3 {
    color: var(--primary-color);
}

.service-card p {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.7;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: var(--primary-color);
    font-weight: 600;
    padding: 0.5rem 0;
    position: relative;
}

.service-link:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.service-link:hover:after {
    width: 100%;
}

.service-link i {
    font-size: 0.8rem;
    transition: var(--transition);
}

.service-link:hover i {
    transform: translateX(5px);
}

/* Services Slider Styles */
.services-slider-container {
    position: relative;
    margin-top: 2rem;
    padding: 1rem 0;
}

.services-slider {
    position: relative;
    overflow: hidden;
    margin: 0 60px;
}

.services-swiper {
    width: 100%;
    padding: 2rem 0 4rem 0;
}

.services-swiper .swiper-slide {
    height: auto;
    display: flex;
    justify-content: center;
}

.service-slide-card {
    background: transparent;
    border: none;
    padding: 1rem 0.5rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    width: 100%;
    max-width: 120px;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.service-slide-card:hover {
    transform: translateY(-5px);
}

.service-slide-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    color: #8B4513;
    font-size: 2.5rem;
    transition: var(--transition);
}

.service-slide-card:hover .service-slide-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.service-slide-card h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    font-size: 0.85rem;
    font-weight: 500;
    transition: var(--transition);
    line-height: 1.2;
}

.service-slide-card:hover h3 {
    color: var(--primary-color);
}

.service-hover-actions {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    z-index: 10;
}

.service-slide-card:hover .service-hover-actions {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.service-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.8rem;
    transition: var(--transition);
    border: 1px solid transparent;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
}

.service-action-btn.know-more {
    background: var(--primary-color);
    color: var(--white);
}

.service-action-btn.know-more:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.service-action-btn.contact-us {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.service-action-btn.contact-us:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Services Slider Navigation */
.services-slider-navigation {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 10;
}

.services-prev,
.services-next {
    position: absolute;
    top: 0;
    width: 40px;
    height: 40px;
    background: var(--white);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
    pointer-events: auto;
}

.services-prev {
    left: -50px;
}

.services-next {
    right: -50px;
}

.services-prev:hover,
.services-next:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-50%) scale(1.1);
}

.services-prev:after,
.services-next:after {
    font-size: 16px !important;
    font-weight: bold;
}

/* Services Pagination */
.services-pagination {
    text-align: center;
    margin-top: 2rem;
}

.services-pagination .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: var(--gray-300);
    opacity: 1;
    margin: 0 6px;
    transition: var(--transition);
}

.services-pagination .swiper-pagination-bullet-active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Services Slider Responsive */
@media (max-width: 1024px) {
    .services-prev {
        left: -40px;
    }

    .services-next {
        right: -40px;
    }

    .services-prev,
    .services-next {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 768px) {
    .services-slider-container {
        margin-top: 2rem;
        padding: 1rem 0;
    }

    .services-slider {
        margin: 0 20px;
    }

    .services-swiper {
        padding: 1rem 0 3rem 0;
    }

    .service-slide-card {
        padding: 0.8rem 0.5rem;
        max-width: 100px;
        min-height: 90px;
    }

    .service-slide-icon {
        width: 50px;
        height: 50px;
        font-size: 2rem;
        margin-bottom: 0.6rem;
    }

    .service-slide-card h3 {
        font-size: 0.75rem;
        margin-bottom: 0.3rem;
    }

    .services-prev,
    .services-next {
        display: none;
    }

    .service-action-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .service-slide-card {
        padding: 0.6rem 0.3rem;
        max-width: 80px;
        min-height: 80px;
    }

    .service-slide-icon {
        width: 40px;
        height: 40px;
        font-size: 1.8rem;
    }

    .service-slide-card h3 {
        font-size: 0.7rem;
    }

    .service-hover-actions {
        gap: 0.3rem;
        padding: 0.8rem;
    }

    .service-action-btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
    }
}

/* Why Choose Us - Enhanced */
.why-choose-us {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
    position: relative;
    overflow: hidden;
}

.why-choose-us:before,
.why-choose-us:after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: var(--primary-color);
    opacity: 0.05;
    z-index: 0;
}

.why-choose-us:before {
    top: -150px;
    left: -150px;
}

.why-choose-us:after {
    bottom: -150px;
    right: -150px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.feature-card {
    padding: 2.5rem 2rem;
    background: var(--white);
    border-radius: var(--card-radius);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.feature-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    opacity: 0.1;
    transition: var(--transition);
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.feature-card:hover:before {
    height: 100%;
}

.feature-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.feature-card:hover i {
    transform: scale(1.1);
    color: var(--primary-dark);
}

.feature-card h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.feature-card p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Stats Section - Enhanced */
.stats-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.stats-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    text-align: center;
    position: relative;
    z-index: 2;
}

.stat-card {
    padding: 1.5rem;
    position: relative;
}

.stat-card:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.stat-card i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.stats-number {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    background: linear-gradient(to right, #fff, #d4e7ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.stat-card p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Testimonials - Enhanced */
.testimonials {
    padding: var(--section-spacing) 0;
    background: var(--white);
    position: relative;
}

.testimonials:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image:
        radial-gradient(var(--gray-200) 1px, transparent 1px),
        radial-gradient(var(--gray-200) 1px, transparent 1px);
    background-size: 50px 50px;
    background-position: 0 0, 25px 25px;
    opacity: 0.5;
}

.testimonials-slider {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.testimonial-card {
    padding: 3rem;
    background: var(--white);
    border-radius: var(--card-radius);
    margin: 1.5rem;
    box-shadow: var(--box-shadow);
    position: relative;
    min-height: 280px; /* Minimum height for consistency */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.testimonial-card:before {
    content: '\f10d';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 2rem;
    color: rgba(44, 123, 229, 0.1);
}

.testimonial-content {
    font-style: italic;
    margin-bottom: 2rem;
    color: var(--secondary-color);
    font-size: 1.1rem;
    line-height: 1.8;
    position: relative;
    z-index: 2;
    flex-grow: 1;
    display: flex;
    align-items: flex-start;
    padding-top: 1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    margin-top: auto; /* Push author info to bottom */
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.testimonial-author img {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--light-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.author-info h4 {
    margin-bottom: 0.3rem;
    font-size: 1.1rem;
}

.author-info p {
    color: var(--primary-color);
    font-size: 0.9rem;
    margin: 0;
}

/* Blog Preview - Enhanced */
.blog-preview {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
    position: relative;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
}

.blog-card {
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.blog-image {
    height: 220px;
    overflow: hidden;
    position: relative;
}

.blog-image:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.7) 100%);
    z-index: 1;
    opacity: 0;
    transition: var(--transition);
}

.blog-card:hover .blog-image:before {
    opacity: 1;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.blog-card:hover .blog-image img {
    transform: scale(1.1);
}

.blog-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.blog-content h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    line-height: 1.4;
    transition: var(--transition);
}

.blog-card:hover .blog-content h3 {
    color: var(--primary-color);
}

.blog-content p {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.read-more {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-top: auto;
}

.read-more:after {
    content: '\f061';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.8rem;
    transition: var(--transition);
}

.read-more:hover:after {
    transform: translateX(5px);
}

/* Contact CTA - Enhanced */
.contact-cta {
    padding: 100px 0;
    background: linear-gradient(rgba(44, 123, 229, 0.95), rgba(44, 123, 229, 0.95)),
                url('../images/cta-bg.jpg') center/cover;
    color: var(--white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-cta:before,
.contact-cta:after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: var(--white);
    opacity: 0.05;
}

.contact-cta:before {
    top: -150px;
    left: -150px;
}

.contact-cta:after {
    bottom: -150px;
    right: -150px;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.cta-content h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: #000000;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.cta-buttons .btn-outline {
    border-color: #2c7be5;
    color: #2c7be5;
}

.cta-buttons .btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* About Page Styles - Enhanced */

/* Team Section */
.team-section {
    padding: var(--section-spacing) 0;
    background: var(--white);
    position: relative;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
}

.team-member {
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.member-image {
    height: 300px;
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-info {
    padding: 2rem;
    text-align: center;
}

.member-info h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.member-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
}

.member-bio {
    color: var(--secondary-color);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Our Story */
.our-story {
    padding: var(--section-spacing) 0;
    background: var(--white);
}

.story-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-content h2 {
    position: relative;
    padding-bottom: 15px;
    margin-bottom: 2rem;
}

.story-content h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.story-content p {
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
    font-size: 1.1rem;
    line-height: 1.8;
}

.story-image {
    position: relative;
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.story-image:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 123, 229, 0.2) 0%, rgba(44, 123, 229, 0) 50%);
    z-index: 1;
}

.story-image img {
    width: 100%;
    border-radius: var(--card-radius);
    transition: var(--transition);
}

.story-image:hover img {
    transform: scale(1.05);
}

/* Mobile and Desktop Story Image Controls */
.mobile-story-image {
    display: none !important;
    margin: 2rem 0;
}

.desktop-story-image {
    display: block !important;
}

/* Ensure proper mobile layout for story section */
@media (max-width: 768px) {
    .story-grid {
        display: block !important;
    }
    
    .mobile-story-image {
        display: block !important;
        margin: 1.5rem 0;
    }
    
    .desktop-story-image {
        display: none !important;
    }
    
    .story-content {
        width: 100%;
    }
}

/* Mission & Vision */
.mission-vision {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
    position: relative;
}

.mission-vision:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-bottom: 5rem;
    position: relative;
    z-index: 2;
}

.mission-box {
    padding: 3.5rem 2.5rem 2.5rem;
    background: var(--white);
    border-radius: var(--card-radius);
    text-align: center;
    position: relative;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.mission-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.mission-box h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.mission-box p {
    color: var(--secondary-color);
    font-size: 1.1rem;
    line-height: 1.7;
}

.mission-icon {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 70px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    box-shadow: 0 5px 15px rgba(44, 123, 229, 0.3);
}

/* Core Values */
.values-grid {
    text-align: center;
    position: relative;
    z-index: 2;
}

.values-grid h2 {
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

.values-grid h2:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.values-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-item {
    padding: 2.5rem 2rem;
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.value-item:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(45deg, rgba(44, 123, 229, 0.05) 0%, rgba(44, 123, 229, 0.1) 100%);
    transition: var(--transition);
    z-index: -1;
}

.value-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.value-item:hover:before {
    height: 100%;
}

.value-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.value-item:hover i {
    transform: rotateY(180deg);
}

.value-item h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.value-item p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Team Section - Enhanced */
.team-section {
    padding: var(--section-spacing) 0;
    background: var(--white);
    position: relative;
}

.team-section:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image:
        radial-gradient(var(--gray-200) 1px, transparent 1px),
        radial-gradient(var(--gray-200) 1px, transparent 1px);
    background-size: 50px 50px;
    background-position: 0 0, 25px 25px;
    opacity: 0.3;
}

.section-subtitle {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--secondary-color);
    font-size: 1.2rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    position: relative;
    z-index: 2;
}

.team-member {
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-15px);
    box-shadow: var(--box-shadow-hover);
}

.member-image {
    height: 300px;
    overflow: hidden;
    position: relative;
}

.member-image:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, transparent 100%);
    z-index: 1;
    opacity: 0;
    transition: var(--transition);
}

.team-member:hover .member-image:before {
    opacity: 1;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-info {
    padding: 2rem;
    text-align: center;
    background: var(--white);
    position: relative;
}

.member-info:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 3px;
}

.member-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.member-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
}

.member-bio {
    color: var(--secondary-color);
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Approach Section - Enhanced */
.approach-section {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
}

.approach-steps {
    max-width: 800px;
    margin: 4rem auto 0;
    position: relative;
}

.approach-steps:before {
    content: '';
    position: absolute;
    top: 0;
    left: 45px;
    height: 100%;
    width: 3px;
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
    border-radius: 3px;
}

.approach-step {
    display: flex;
    align-items: flex-start;
    gap: 2.5rem;
    margin-bottom: 3rem;
    position: relative;
}

.approach-step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 90px;
    height: 90px;
    background: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(44, 123, 229, 0.3);
    z-index: 2;
    position: relative;
}

.step-content {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    flex-grow: 1;
}

.approach-step:hover .step-content {
    transform: translateX(10px);
    box-shadow: var(--box-shadow-hover);
}

.step-content h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.step-content p {
    color: var(--secondary-color);
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 0;
}
/* Additional Custom Animations */
.fadeInUp {
    animation: fadeInUp 1s ease-out;
}

.fadeInLeft {
    animation: fadeInLeft 1s ease-out;
}

.fadeInRight {
    animation: fadeInRight 1s ease-out;
}

.bounceIn {
    animation: bounceIn 1s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
    }
}

/* Enhanced Form Elements */
.form-group input.error,
.form-group textarea.error,
.form-group select.error {
    border-color: var(--error-color);
    background-color: rgba(239, 68, 68, 0.05);
}

.form-group input.error:focus,
.form-group select.error:focus,
.form-group textarea.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group .error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
    font-weight: 500;
    animation: fadeIn 0.3s ease;
}

.char-counter {
    color: var(--secondary-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    text-align: right;
}

/* Enhanced Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 1050;
    display: none;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    position: relative;
    background: var(--white);
    max-width: 550px;
    width: 90%;
    margin: 2rem auto;
    padding: 2.5rem;
    border-radius: var(--card-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 0.5s ease-out;
    text-align: center;
}

.close {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
    transition: var(--transition);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--gray-100);
}

.close:hover {
    color: var(--error-color);
    background: var(--gray-200);
}

.success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1.5rem;
    display: inline-block;
    animation: bounceIn 0.8s ease;
}

.modal-content h2 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.modal-content p {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

.reference-number {
    margin-top: 1.5rem;
    padding: 1.2rem;
    background: var(--light-color);
    border-radius: 8px;
    font-family: monospace;
    font-size: 1.2rem;
    letter-spacing: 1px;
    color: var(--primary-color);
    font-weight: 600;
}

/* Enhanced FAQ Styles */
.faq-question:hover h3 {
    color: var(--primary-color);
}

.faq-answer p {
    color: var(--secondary-color);
    line-height: 1.7;
}

/* Pricing Section Enhancements */
.pricing-hero {
    background-image:linear-gradient(rgb(63 135 231), rgb(18 103 117)),url('../images/pricing-hero.jpg');
}

.pricing-plans {
    padding: var(--section-spacing) 0;
    background: var(--white);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    margin: 2rem auto 0;
    max-width: 1200px;
}

.pricing-plan {
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--gray-200);
}

.pricing-plan:hover {
    transform: translateY(-15px);
    box-shadow: var(--box-shadow-hover);
}

.plan-header {
    padding: 2.5rem 1.5rem;
    text-align: center;
    background: var(--light-color);
    border-bottom: 1px solid var(--gray-200);
}

.pricing-plan.recommended .plan-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding-top: 3.5rem;
}

.pricing-plan.recommended h2,
.pricing-plan.recommended .plan-name,
.pricing-plan.recommended .plan-description {
    color: var(--white);
}

.plan-badge {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: var(--accent-color);
    color: var(--white);
    text-align: center;
    padding: 0.6rem 0;
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.plan-name {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.plan-price {
    margin: 1.5rem 0;
}

.currency {
    font-size: 1.5rem;
    vertical-align: text-top;
    margin-right: 0.25rem;
    opacity: 0.8;
}

.amount {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1;
}

.period {
    font-size: 1rem;
    color: var(--secondary-color);
    opacity: 0.8;
}

.pricing-plan.recommended .period {
    color: rgba(255, 255, 255, 0.8);
}

.plan-description {
    color: var(--secondary-color);
    font-size: 0.95rem;
    margin: 0 auto;
    max-width: 250px;
}

.plan-features {
    list-style: none;
    padding: 2.5rem 2rem;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    margin-bottom: 1.2rem;
    color: var(--secondary-color);
    font-size: 0.95rem;
}

.plan-features li:last-child {
    margin-bottom: 0;
}

.plan-features i {
    color: var(--primary-color);
    font-size: 1rem;
    margin-top: 0.2rem;
}

.pricing-plan.recommended .btn-primary {
    background: var(--white);
    color: var(--primary-color);
}

.pricing-plan.recommended .btn-primary:hover {
    background: var(--accent-color);
    color: var(--white);
}

.pricing-plan .btn {
    margin: 0 2rem 2.5rem;
    width: calc(100% - 4rem);
}

/* Enhanced Comparison Table */
.comparison-section {
    padding: var(--section-spacing) 0;
    background: var(--light-color);
}

.comparison-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

.comparison-section h2:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.comparison-table-wrapper {
    overflow-x: auto;
    margin: 0 auto;
    max-width: 1000px;
    box-shadow: var(--box-shadow);
    border-radius: var(--card-radius);
}

.comparison-table {
    width: 100%;
    min-width: 800px;
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
}

.comparison-table th,
.comparison-table td {
    padding: 1.2rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid var(--gray-200);
}

.comparison-table th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    font-weight: 600;
    font-size: 1rem;
}

.comparison-table th:first-child {
    text-align: left;
    border-top-left-radius: var(--card-radius);
}

.comparison-table th:last-child {
    border-top-right-radius: var(--card-radius);
}

.comparison-table td:first-child {
    text-align: left;
    font-weight: 600;
    color: var(--dark-color);
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table tr:nth-child(odd) {
    background-color: rgba(248, 250, 252, 0.5);
}

.comparison-table tr:hover {
    background-color: rgba(44, 123, 229, 0.05);
}

.comparison-table i.fa-check {
    color: var(--success-color);
    font-size: 1.2rem;
}

.comparison-table i.fa-times {
    color: var(--error-color);
    font-size: 1.2rem;
}

/* Enhanced Error Page Style */
.error-hero {
    padding: 180px 0 100px;
    background: var(--light-color);
    text-align: center;
}

.error-content {
    max-width: 700px;
    margin: 0 auto;
}

.error-icon {
    font-size: 8rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    animation: bounceIn 1s ease;
}

.error-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.error-help {
    padding: 80px 0;
    background: var(--white);
}

.error-help h2 {
    text-align: center;
    margin-bottom: 3.5rem;
    position: relative;
    display: inline-block;
}

.error-help h2:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.help-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
    text-align: center;
}

.help-option {
    padding: 2.5rem 2rem;
    background: var(--light-color);
    border-radius: var(--card-radius);
    transition: var(--transition);
    box-shadow: var(--box-shadow-sm);
}

.help-option:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow);
}

.help-option i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.help-option h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-size: 1.3rem;
}

.help-option p {
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
}/* Fixed Header Page Padding */
.fixed-header-padding {
    padding-top: 100px !important; /* Add padding to account for fixed header */
}

/* FAQ Page Specific Styles */
.accordion {
    margin-bottom: 3rem;
}

.accordion-item {
    margin-bottom: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow-sm);
    background-color: var(--white);
    transition: var(--transition);
}

.accordion-item:hover {
    box-shadow: var(--box-shadow);
}

.accordion-header {
    width: 100%;
}

/* Bootstrap Accordion Overrides for FAQ Page */
.accordion {
    margin-bottom: 3rem;
}

.accordion-item {
    margin-bottom: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--card-radius) !important;
    overflow: hidden;
    box-shadow: var(--box-shadow-sm);
    background-color: var(--white);
    transition: var(--transition);
}

.accordion-item:hover {
    box-shadow: var(--box-shadow);
}

.accordion-header {
    width: 100%;
    margin: 0;
}

.accordion-button {
    padding: 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--dark-color);
    background-color: var(--white);
    border: none;
    text-align: left;
    transition: var(--transition);
    position: relative;
}

.accordion-button:not(.collapsed) {
    color: var(--primary-color);
    background-color: rgba(44, 123, 229, 0.05);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(44, 123, 229, 0.1);
}

/* Fix for the arrow icon */
.accordion-button::after {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: auto;
    content: "";
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232C7BE5'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 1.25rem;
    transition: transform 0.2s ease-in-out;
}

.accordion-button.collapsed::after {
    transform: rotate(-90deg);
}

.accordion-collapse {
    transition: all 0.3s ease;
    border: 0;
}

.accordion-body {
    padding: 0 1.5rem 1.5rem;
}

.accordion-body p {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    line-height: 1.7;
}

.accordion-body ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.accordion-body ul li {
    margin-bottom: 0.5rem;
    line-height: 1.7;
}

/* Fix for Bootstrap button styling */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--button-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
    padding: 0.75rem 1.5rem;
    border-radius: var(--button-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

/* Category Filter */
.category-filter {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.category-filter .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
}
  /* Footer Icons Fix */
.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .social-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition);
    font-size: 1rem;
    color: var(--white);
  }

  .social-icon:hover {
    background: var(--primary-color);
    transform: translateY(-5px) rotate(10deg);
  }

  /* Specific styles for each social icon */
  .social-icon.facebook {
    background: rgba(59, 89, 152, 0.2);
  }
  .social-icon.facebook:hover {
    background: #3b5998;
  }

  .social-icon.tiktok {
    background: rgba(0, 0, 0, 0.2);
  }
  .social-icon.tiktok:hover {
    background: #000000;
  }

  .social-icon.instagram {
    background: rgba(225, 48, 108, 0.2);
  }
  .social-icon.instagram:hover {
    background: #e1306c;
  }

  .social-icon.linkedin {
    background: rgba(0, 119, 181, 0.2);
  }
  .social-icon.linkedin:hover {
    background: #0077b5;
  }

  .social-icon.youtube {
    background: rgba(255, 0, 0, 0.2);
  }
  .social-icon.youtube:hover {
    background: #ff0000;
  }

  /* Footer newsletter form fix */
  .newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    position: relative;
  }

  .newsletter-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: var(--button-radius);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: 0.95rem;
    min-width: 0; /* Ensures proper sizing in flex container */
  }

  .newsletter-form button {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
  }
/* Add this to your styles.css file */

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: var(--white);
    position: relative;
}

.testimonials-slider {
    position: relative;
    margin: 40px auto;
    max-width: 1000px;
    padding: 0 40px;
}

/* Testimonial Card Styles - Flexible height for full content */
.testimonial-card {
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 20px 10px;
    transition: all 0.3s ease;
    min-height: 280px; /* Minimum height for consistency */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.testimonial-content {
    font-style: italic;
    color: var(--secondary-color);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    flex-grow: 1;
    display: flex;
    align-items: flex-start;
    padding-top: 1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: auto; /* Push author info to bottom */
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.testimonial-author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.testimonial-author-image.no-image {
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.author-info h4 {
    margin: 0 0 5px;
    font-size: 1.1rem;
    color: var(--dark-color);
}

.author-info p {
    margin: 0;
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* Swiper Specific Styling */
.testimonials-swiper {
    overflow: hidden;
    padding-bottom: 40px;
}

.testimonials-swiper .swiper-wrapper {
    align-items: stretch; /* Make all slides the same height */
}

.testimonials-swiper .swiper-slide {
    height: auto; /* Allow slides to grow to content */
    display: flex;
    align-items: stretch;
}

/* Ensure testimonial cards within the same view have equal height */
.testimonials-swiper .swiper-slide .testimonial-card {
    width: 100%;
    margin: 1.5rem 0.75rem; /* Adjust margins for better spacing */
}

/* Better spacing for testimonial author section */
.testimonial-author {
    margin-top: auto; /* Push author info to bottom */
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

/* Global Swiper pagination styles (excluding hero slider) */
.swiper:not(.hero-swiper) .swiper-pagination {
    bottom: 0 !important;
}

.swiper:not(.hero-swiper) .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    opacity: 0.3;
}

.swiper:not(.hero-swiper) .swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--primary-color);
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--primary-color) !important;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 20px !important;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .testimonials-slider {
        padding: 0 20px;
    }

    .testimonial-card {
        padding: 20px;
        min-height: 250px; /* Minimum height for mobile */
    }

    .testimonial-content {
        padding-top: 0.5rem; /* Reduced padding for mobile */
    }

    .swiper-button-next,
    .swiper-button-prev {
        display: none;
    }
}
/* Add this to your css/styles.css file - Mobile Menu Fix */

/* Hamburger Menu Toggle Button */
.nav-toggle {
    display: none; /* Hidden by default on desktop */
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
    position: relative;
}

.nav-toggle span {
    display: block;
    width: 100%;
    height: 3px;
    background: var(--dark-color);
    border-radius: 3px;
    transition: all 0.3s ease;
}

/* Active state of the hamburger menu */
.nav-toggle.active span:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.nav-toggle.active span:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Mobile menu styles - ensure these are properly defined */
@media (max-width: 992px) {
    .nav-toggle {
        display: flex; /* Show hamburger menu on mobile */
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 2rem 0;
        z-index: 1000;
        overflow-y: auto;
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
        box-shadow: var(--box-shadow);
    }

    .nav-link {
        padding: 1rem 2rem;
        width: 100%;
        text-align: center;
    }

    .cta-button {
        margin-top: 1rem;
    }
}
/* Blog Page Styles */

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(rgba(44, 123, 229, 0.9), rgba(44, 123, 229, 0.9)),
                url('../images/blog-hero.jpg') center/cover;
    padding: 120px 0 80px;
}

.blog-hero h1 {
    color: var(--white);
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.blog-hero p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
}

/* Blog Section */
.blog-section {
    padding: 80px 0;
    background: var(--white);
}

.blog-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

/* Blog Main Content */
.blog-main {
    width: 100%;
}

/* Dynamic Masonry Layout */
.blog-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
    align-items: start;
}

/* Dynamic Blog Cards */
.blog-card {
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    break-inside: avoid;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

/* Dynamic Image Container */
.blog-image {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.blog-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
    z-index: 2;
}

.blog-card:hover .blog-image::before {
    transform: translateX(100%);
}

.blog-image img {
    width: 100%;
    height: auto;
    min-height: 200px;
    max-height: 350px;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: scale(1.1);
}

.blog-image img.loaded {
    opacity: 1;
    transform: scale(1);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

/* Dynamic Placeholder with Animation */
.placeholder-image {
    width: 100%;
    min-height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 2.5rem;
    position: relative;
    overflow: hidden;
}

.placeholder-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(0,0,0,0.02) 10px,
        rgba(0,0,0,0.02) 20px
    );
    animation: shimmer 3s linear infinite;
}

.placeholder-image i {
    position: relative;
    z-index: 2;
    margin-bottom: 10px;
    animation: pulse 2s ease-in-out infinite;
}

.placeholder-image::after {
    content: 'No Image Available';
    position: relative;
    z-index: 2;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.7;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%); }
    100% { transform: translateX(100%) translateY(100%); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Loading Skeleton */
.blog-image.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Dynamic Content Area */
.blog-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background: var(--white);
    transition: all 0.3s ease;
}

.blog-card:hover .blog-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* Content Preview Overlay */
.blog-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.9) 70%, rgba(255,255,255,1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.blog-card:hover .blog-content::before {
    opacity: 1;
}

/* Enhanced Content Preview */
.content-preview {
    position: absolute;
    bottom: 25px;
    left: 25px;
    right: 25px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 15px;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-card:hover .content-preview {
    transform: translateY(0);
    opacity: 1;
}

.content-preview p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--secondary-color);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.blog-date, .blog-category, .post-views {
    display: flex;
    align-items: center;
    gap: 5px;
}

.blog-category a {
    color: var(--primary-color);
    transition: var(--transition);
}

.blog-category a:hover {
    text-decoration: underline;
}

.blog-content h2 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    line-height: 1.4;
}

.blog-content h2 a {
    color: var(--dark-color);
    transition: var(--transition);
}

.blog-content h2 a:hover {
    color: var(--primary-color);
}

.blog-content p {
    color: var(--secondary-color);
    margin-bottom: 20px;
    flex-grow: 1;
}

.read-more {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-top: auto;
    transition: var(--transition);
}

.read-more i {
    font-size: 0.8rem;
    transition: var(--transition);
}

.read-more:hover {
    color: var(--primary-dark);
}

.read-more:hover i {
    transform: translateX(5px);
}

/* No Posts Found */
.no-posts {
    text-align: center;
    padding: 50px 20px;
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
}

.no-posts h2 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.no-posts p {
    color: var(--secondary-color);
    max-width: 500px;
    margin: 0 auto;
}

/* Blog Sidebar */
.blog-sidebar {
    width: 100%;
}

.sidebar-widget {
    background: var(--white);
    border-radius: var(--card-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.sidebar-widget h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--gray-200);
    position: relative;
}

.sidebar-widget h3:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Search Widget */
.search-widget {
    padding: 0;
    overflow: hidden;
}

.search-form {
    display: flex;
}

.search-form input {
    flex: 1;
    padding: 15px;
    border: none;
    background: var(--white);
    font-family: inherit;
}

.search-form input:focus {
    outline: none;
}

.search-form button {
    width: 50px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.search-form button:hover {
    background: var(--primary-dark);
}

/* Category List */
.category-list {
    list-style: none;
}

.category-list li {
    margin-bottom: 12px;
    border-bottom: 1px dashed var(--gray-200);
    padding-bottom: 12px;
}

.category-list li:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.category-list a {
    display: flex;
    justify-content: space-between;
    color: var(--dark-color);
    transition: var(--transition);
}

.category-list a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.category-count {
    color: var(--secondary-color);
}

/* Recent Posts */
.recent-posts {
    list-style: none;
}

.recent-posts li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--gray-200);
}

.recent-posts li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.recent-posts a {
    display: flex;
    gap: 15px;
    color: inherit;
    transition: var(--transition);
}

.recent-posts a:hover {
    color: var(--primary-color);
}

.post-thumbnail {
    width: 80px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    flex-shrink: 0;
}

.post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-info {
    flex: 1;
}

.post-info h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.post-date {
    font-size: 0.85rem;
    color: var(--secondary-color);
}

/* Tag Cloud */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag {
    display: inline-block;
    padding: 5px 12px;
    background: var(--gray-100);
    color: var(--secondary-color);
    border-radius: 20px;
    font-size: 0.85rem;
    transition: var(--transition);
}

.tag:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
}

/* CTA Section */
.blog-cta {
    padding: 80px 0;
    background: linear-gradient(rgba(44, 123, 229, 0.9), rgba(44, 123, 229, 0.9)),
                url('../images/cta-bg.jpg') center/cover;
    text-align: center;
    color: var(--white);
}

.blog-cta h2 {
    color: var(--white);
    margin-bottom: 20px;
    font-size: 2.2rem;
}

.blog-cta p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.1rem;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.blog-cta .btn-outline {
    border-color: var(--white);
    color: var(--white);
}

.blog-cta .btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 50px;
}

.pagination-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 15px;
    background: var(--white);
    color: var(--dark-color);
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
}

.pagination-item:hover {
    background: var(--light-color);
}

.pagination-item.active {
    background: var(--primary-color);
    color: var(--white);
}

.pagination-ellipsis {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

/* Blog Post Page Styles */

/* Blog Post Hero */
.blog-post-hero {
    background: linear-gradient(rgba(26, 43, 60, 0.85), rgba(26, 43, 60, 0.85)),
                url('../images/blog-post-hero.jpg') center/cover;
    text-align: center;
}

.blog-post-hero .post-meta {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.8);
}

.blog-post-hero .post-meta a {
    color: var(--primary-light);
}

.blog-post-hero .post-meta a:hover {
    text-decoration: underline;
}

/* Blog Post Content */
.blog-post-section {
    padding: 80px 0;
    background: var(--white);
}

.blog-post-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.blog-post-main {
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.post-featured-image {
    width: 100%;
    height: auto;
    max-height: 500px;
    overflow: hidden;
}

.post-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-content {
    padding: 40px;
}

.post-content h2 {
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.post-content h3 {
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.post-content p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--secondary-color);
}

.post-content ul, .post-content ol {
    margin-bottom: 20px;
    margin-left: 20px;
}

.post-content li {
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.post-content blockquote {
    padding: 20px;
    background: var(--light-color);
    border-left: 4px solid var(--primary-color);
    margin: 30px 0;
    font-style: italic;
    color: var(--dark-color);
}

.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
}

.post-content a {
    color: var(--primary-color);
    text-decoration: underline;
}

.post-content a:hover {
    color: var(--primary-dark);
}

/* Post Tags */
.post-tags {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.tags-label {
    font-weight: 600;
    color: var(--dark-color);
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Author Info */
.post-author {
    margin-top: 40px;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    display: flex;
    align-items: center;
    gap: 20px;
}

.author-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0;
}

.author-info h3 {
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.author-info p {
    color: var(--secondary-color);
    margin-bottom: 5px;
}

/* Post Navigation */
.post-navigation {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.post-nav-link {
    flex: 1;
    padding: 20px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    color: var(--dark-color);
    transition: var(--transition);
}

.post-nav-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-5px);
}

.prev-post {
    text-align: left;
}

.next-post {
    text-align: right;
}

.post-nav-label {
    display: block;
    font-size: 0.85rem;
    margin-bottom: 5px;
    opacity: 0.8;
}

.post-nav-title {
    font-weight: 600;
}

/* Comments Section */
.comments-section {
    margin-top: 60px;
}

.comments-section h2 {
    margin-bottom: 30px;
    font-size: 1.8rem;
}

.comment-list {
    margin-bottom: 40px;
}

.comment {
    display: flex;
    gap: 20px;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    margin-bottom: 20px;
}

.comment.reply {
    margin-left: 60px;
    margin-top: 20px;
    background: var(--white);
    border: 1px solid var(--gray-200);
}

.comment-avatar {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.comment-content {
    flex: 1;
}

.comment-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.comment-author {
    margin: 0;
    font-size: 1.1rem;
}

.comment-date {
    font-size: 0.85rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

.comment-text {
    margin-bottom: 15px;
    color: var(--secondary-color);
    line-height: 1.7;
}

.comment-actions {
    display: flex;
    gap: 15px;
}

.reply-button {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 0;
    transition: var(--transition);
}

.reply-button:hover {
    color: var(--primary-dark);
}

.comment-replies {
    margin-top: 20px;
}

/* Comment Form */
.comment-form-container {
    background: var(--white);
    border-radius: var(--card-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    margin-top: 40px;
}

.comment-form-container h3 {
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.comment-success {
    padding: 15px;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-radius: 5px;
    margin-bottom: 20px;
}

.comment-error {
    padding: 15px;
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border-radius: 5px;
    margin-bottom: 20px;
}

.comment-form .form-group {
    margin-bottom: 20px;
}

.comment-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.comment-form input,
.comment-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--gray-200);
    border-radius: 5px;
    font-family: inherit;
    transition: var(--transition);
}

.comment-form input:focus,
.comment-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 123, 229, 0.1);
}

.comment-form .form-actions {
    display: flex;
    gap: 15px;
}

.btn-cancel-reply {
    padding: 10px 20px;
    background: var(--gray-200);
    color: var(--secondary-color);
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-cancel-reply:hover {
    background: var(--gray-300);
}

.btn-submit {
    padding: 10px 25px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-submit:hover {
    background: var(--primary-dark);
}

.reply-form-container {
    margin-top: 20px;
    padding: 20px;
    background: var(--white);
    border-radius: 5px;
    border: 1px solid var(--gray-200);
}

.no-comments {
    text-align: center;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    margin-bottom: 30px;
}

/* Related Posts in Sidebar */
.related-posts-widget h3 {
    color: var(--primary-color);
}

/* Media Queries */
@media (max-width: 1200px) {
    .blog-grid,
    .blog-post-grid {
        gap: 30px;
    }

    .cta-buttons {
        flex-direction: column;
        max-width: 250px;
        margin: 0 auto;
    }
}

@media (max-width: 991px) {
    .blog-grid,
    .blog-post-grid {
        grid-template-columns: 1fr;
    }

    .blog-sidebar {
        order: -1;
    }

    .blog-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }

    .post-navigation {
        flex-direction: column;
    }

    .post-content {
        padding: 30px;
    }

    .comment {
        padding: 20px;
    }

    .comment.reply {
        margin-left: 30px;
    }
}

@media (max-width: 768px) {
    .blog-hero h1 {
        font-size: 2.2rem;
    }

    .blog-hero p {
        font-size: 1rem;
    }

    .blog-posts-grid {
        grid-template-columns: 1fr;
    }

    .blog-image {
        height: 200px;
    }

    .post-author {
        flex-direction: column;
        text-align: center;
    }

    .author-avatar {
        margin: 0 auto;
    }

    .comment {
        flex-direction: column;
        align-items: flex-start;
    }

    .comment-meta {
        justify-content: flex-start;
    }
}

@media (max-width: 480px) {
    .blog-section,
    .blog-post-section {
        padding: 50px 0;
    }

    .blog-content {
        padding: 20px;
    }

    .post-content {
        padding: 20px;
    }

    .post-featured-image {
        max-height: 300px;
    }

    .comment-form-container {
        padding: 20px;
    }

    .blog-cta h2 {
        font-size: 1.8rem;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}
/* Add this to your css/styles.css file - Mobile Menu Fix */

/* Hamburger Menu Toggle Button */
.nav-toggle {
    display: none; /* Hidden by default on desktop */
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
    position: relative;
}

.nav-toggle span {
    display: block;
    width: 100%;
    height: 3px;
    background: var(--dark-color);
    border-radius: 3px;
    transition: all 0.3s ease;
}

/* Active state of the hamburger menu */
.nav-toggle.active span:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.nav-toggle.active span:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Social Media Sharing Styles */
.blog-social-share {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 15px 0;
    padding: 15px 0;
    border-top: 1px solid var(--gray-200);
}

.share-label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.social-share {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-share:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0.9;
}

.social-share:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.social-share.facebook {
    background: #1877f2;
}

.social-share.twitter {
    background: #1da1f2;
}

.social-share.linkedin {
    background: #0077b5;
}

.social-share.whatsapp {
    background: #25d366;
}

.social-share.whatsapp:hover {
    background: #128c7e;
}

/* Add loading state for social sharing */
.social-share.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Improve accessibility */
.social-share:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Post Social Sharing */
.post-social-share {
    margin: 40px 0;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    text-align: center;
}

.post-social-share h3 {
    margin-bottom: 20px;
    color: var(--dark-color);
    font-size: 1.3rem;
}

.social-share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.post-social-share .social-share {
    width: auto;
    height: auto;
    padding: 12px 20px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    font-size: 0.9rem;
}

.post-social-share .social-share span {
    display: inline-block;
}

.post-social-share .social-share.email {
    background: var(--secondary-color);
}

/* Justified Text */
.justified-text,
.blog-excerpt {
    text-align: justify;
    line-height: 1.7;
}

.justified-text p {
    text-align: justify;
    margin-bottom: 1.5rem;
}

/* Enhanced Blog Card Excerpt */
.blog-excerpt {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Responsive Social Sharing */
@media (max-width: 768px) {
    .blog-social-share {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .social-share-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .post-social-share .social-share-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .post-social-share .social-share {
        justify-content: center;
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
    }
}

/* Mobile menu styles - ensure these are properly defined */
@media (max-width: 992px) {
    .nav-toggle {
        display: flex; /* Show hamburger menu on mobile */
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 2rem 0;
        z-index: 1000;
        overflow-y: auto;
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
        box-shadow: var(--box-shadow);
    }

    .nav-link {
        padding: 1rem 2rem;
        width: 100%;
        text-align: center;
    }

    .cta-button {
        margin-top: 1rem;
    }
}
/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 1000;
}

.scroll-to-top i {
    font-size: 1.2rem;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    box-shadow: 0 8px 25px rgba(44, 123, 229, 0.4);
    transform: translateY(-5px);
}

/* Pulse animation */
@keyframes pulse {
    0% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-5px) scale(1.05);
    }
    100% {
        transform: translateY(0) scale(1);
    }
}

.scroll-to-top:hover {
    animation: pulse 1s infinite;
}

/* ===================================
   BEAUTIFUL RED & BLUE DESIGN SYSTEM
   =================================== */

/* Enhanced Button Variants */
.btn-gradient {
    background: var(--gradient-red-blue);
    color: var(--white);
    box-shadow: var(--box-shadow-red);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-red-hover);
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    transition: var(--transition);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Beautiful Card Components */
.card-premium {
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-red-blue);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.card-premium:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-lg);
}

.card-premium:hover::before {
    transform: scaleX(1);
}

/* Icon Enhancement */
.icon-container {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.icon-red {
    background: var(--primary-ultra-light);
    color: var(--primary-color);
    border: 2px solid transparent;
}

.icon-red:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1) rotate(5deg);
    border-color: var(--primary-light);
    box-shadow: var(--box-shadow-red);
}

.icon-blue {
    background: var(--secondary-ultra-light);
    color: var(--secondary-color);
    border: 2px solid transparent;
}

.icon-blue:hover {
    background: var(--secondary-color);
    color: var(--white);
    transform: scale(1.1) rotate(-5deg);
    border-color: var(--secondary-light);
    box-shadow: var(--box-shadow-blue);
}

/* Text and Background Utilities */
.text-red {
    color: var(--primary-color) !important;
}

.text-blue {
    color: var(--secondary-color) !important;
}

.text-muted {
    color: var(--accent-color) !important;
}

.bg-red-gradient {
    background: var(--gradient-red) !important;
    color: var(--white);
}

.bg-blue-gradient {
    background: var(--gradient-blue) !important;
    color: var(--white);
}

.bg-red-light {
    background: var(--primary-ultra-light) !important;
}

.bg-blue-light {
    background: var(--secondary-ultra-light) !important;
}