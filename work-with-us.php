<?php
require_once 'includes/config.php';
require_once 'includes/Jobs.php';
require_once 'includes/Settings.php';

$jobs = new Jobs();
$settingsHandler = new Settings();

// Get active jobs
$activeJobs = $jobs->getAllJobs(true);

// Check for success message
$success = isset($_GET['success']) && $_GET['success'] == '1';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Work With Us - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="Join our team at <?php echo SITE_NAME; ?>. Explore career opportunities in home healthcare and make a difference in people's lives.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo $settingsHandler->get('favicon', 'images/favicon.ico'); ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    
    <!-- Google reCAPTCHA -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <!-- Hero Section -->
    <section class="hero-section work-with-us-hero">
        <div class="hero-content">
            <div class="container">
                <div class="hero-text" data-aos="fade-up">
                    <h1>Work With Us</h1>
                    <p>Join our dedicated team and make a meaningful difference in people's lives through quality home healthcare services.</p>
                </div>
            </div>
        </div>
        <div class="hero-image">
            <img src="images/hero-banner.jpg" alt="Healthcare Team" loading="lazy">
        </div>
    </section>
    
    <?php if ($success): ?>
        <div class="container">
            <div class="alert alert-success" data-aos="fade-in">
                <i class="fas fa-check-circle"></i>
                <strong>Application Submitted Successfully!</strong>
                <p>Thank you for your interest in joining our team. We have received your application and will review it carefully. We'll contact you if your qualifications match our current openings.</p>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Career Introduction -->
    <section class="career-intro">
        <div class="container">
            <div class="content-wrapper" data-aos="fade-up">
                <h2>Make Career With Us</h2>
                <p>Serving in home healthcare is a rewarding job as it gives you the opportunity to make a profound difference in people's life every day. We serve people to help them lead their life with independence, and self-esteem. As an employee of <?php echo SITE_NAME; ?> you get the platform in the pioneer firm working in home healthcare. Working here will be an opportunity to enhance your clinical, management, and leadership skills.</p>
            </div>
        </div>
    </section>
    
    <!-- Job Listings -->
    <section class="job-listings">
        <div class="container">
            <h2 data-aos="fade-up">Current Openings</h2>
            
            <?php if (!empty($activeJobs)): ?>
                <div class="jobs-grid">
                    <?php foreach ($activeJobs as $job): ?>
                        <div class="job-card" data-aos="fade-up" data-aos-delay="100">
                            <div class="job-header">
                                <h3><?php echo htmlspecialchars($job['title']); ?></h3>
                                <div class="job-meta">
                                    <?php if ($job['location']): ?>
                                        <span class="location">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($job['location']); ?>
                                        </span>
                                    <?php endif; ?>
                                    <span class="job-type">
                                        <i class="fas fa-clock"></i>
                                        <?php echo ucfirst(str_replace('_', ' ', $job['job_type'])); ?>
                                    </span>
                                    <?php if ($job['salary_range']): ?>
                                        <span class="salary">
                                            <i class="fas fa-money-bill-wave"></i>
                                            <?php echo htmlspecialchars($job['salary_range']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="job-content">
                                <div class="job-description">
                                    <p><?php echo nl2br(htmlspecialchars(substr($job['description'], 0, 200))); ?><?php echo strlen($job['description']) > 200 ? '...' : ''; ?></p>
                                </div>
                                
                                <?php if ($job['requirements']): ?>
                                    <div class="job-requirements">
                                        <h4>Key Requirements:</h4>
                                        <ul>
                                            <?php 
                                            $requirements = explode("\n", $job['requirements']);
                                            $displayRequirements = array_slice($requirements, 0, 3);
                                            foreach ($displayRequirements as $requirement): 
                                                $requirement = trim($requirement);
                                                if (!empty($requirement)):
                                            ?>
                                                <li><?php echo htmlspecialchars($requirement); ?></li>
                                            <?php 
                                                endif;
                                            endforeach; 
                                            ?>
                                            <?php if (count($requirements) > 3): ?>
                                                <li><em>And more...</em></li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="job-actions">
                                    <button class="btn btn-primary apply-btn" data-job-id="<?php echo $job['id']; ?>" data-job-title="<?php echo htmlspecialchars($job['title']); ?>">
                                        Apply Now
                                    </button>
                                    <button class="btn btn-secondary view-details-btn" data-job-id="<?php echo $job['id']; ?>">
                                        View Details
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Hidden job details for modal -->
                            <div class="job-details-hidden" id="job-details-<?php echo $job['id']; ?>" style="display: none;">
                                <div class="full-description">
                                    <h4>Job Description</h4>
                                    <p><?php echo nl2br(htmlspecialchars($job['description'])); ?></p>
                                </div>
                                
                                <?php if ($job['requirements']): ?>
                                    <div class="full-requirements">
                                        <h4>Requirements</h4>
                                        <div><?php echo nl2br(htmlspecialchars($job['requirements'])); ?></div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($job['responsibilities']): ?>
                                    <div class="full-responsibilities">
                                        <h4>Responsibilities</h4>
                                        <div><?php echo nl2br(htmlspecialchars($job['responsibilities'])); ?></div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="job-info">
                                    <div class="info-item">
                                        <strong>Experience Level:</strong> <?php echo ucfirst($job['experience_level']); ?>
                                    </div>
                                    <?php if ($job['application_deadline']): ?>
                                        <div class="info-item">
                                            <strong>Application Deadline:</strong> <?php echo date('F j, Y', strtotime($job['application_deadline'])); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-jobs" data-aos="fade-up">
                    <div class="no-jobs-content">
                        <i class="fas fa-briefcase"></i>
                        <h3>No Current Openings</h3>
                        <p>We don't have any open positions at the moment, but we're always looking for talented individuals to join our team. Please check back later or submit your resume for future opportunities.</p>
                        <button class="btn btn-primary apply-btn" data-job-id="general" data-job-title="General Application">
                            Submit Resume for Future Opportunities
                        </button>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
    
    <!-- Application Process -->
    <section class="application-process">
        <div class="container">
            <h2 data-aos="fade-up">Application Process</h2>
            <div class="process-steps">
                <div class="step" data-aos="fade-up" data-aos-delay="100">
                    <div class="step-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>1. Submit Application</h3>
                    <p>Fill out our application form and upload your resume</p>
                </div>
                
                <div class="step" data-aos="fade-up" data-aos-delay="200">
                    <div class="step-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>2. Review Process</h3>
                    <p>Our HR team will review your application and qualifications</p>
                </div>
                
                <div class="step" data-aos="fade-up" data-aos-delay="300">
                    <div class="step-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>3. Interview</h3>
                    <p>Qualified candidates will be contacted for an interview</p>
                </div>
                
                <div class="step" data-aos="fade-up" data-aos-delay="400">
                    <div class="step-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3>4. Join Our Team</h3>
                    <p>Welcome to our healthcare family!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Job Details Modal -->
    <div id="jobDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalJobTitle">Job Details</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body" id="modalJobContent">
                <!-- Job details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="modalApplyBtn">Apply for this Position</button>
                <button class="btn btn-secondary modal-close">Close</button>
            </div>
        </div>
    </div>

    <!-- Application Modal -->
    <div id="applicationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Apply for Position</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="applicationForm" action="https://formspree.io/f/xqabgezq" method="POST" enctype="multipart/form-data">
                    <!-- Hidden fields for Formspree -->
                    <input type="hidden" name="_subject" value="New Job Application - <?php echo SITE_NAME; ?>">
                    <input type="hidden" name="_next" value="<?php echo SITE_URL; ?>/work-with-us.php?success=1">
                    <input type="hidden" name="job_title" id="applicationJobTitle">
                    <input type="hidden" name="job_id" id="applicationJobId">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="applicant_name">Full Name *</label>
                            <input type="text" id="applicant_name" name="applicant_name" required>
                        </div>

                        <div class="form-group">
                            <label for="applicant_email">Email Address *</label>
                            <input type="email" id="applicant_email" name="applicant_email" required>
                        </div>

                        <div class="form-group">
                            <label for="applicant_phone">Phone Number *</label>
                            <input type="tel" id="applicant_phone" name="applicant_phone" required>
                        </div>

                        <div class="form-group">
                            <label for="preferred_position">Preferred Position</label>
                            <input type="text" id="preferred_position" name="preferred_position" readonly>
                        </div>

                        <div class="form-group full-width">
                            <label for="cover_letter">Cover Letter / Why do you want to work with us? *</label>
                            <textarea id="cover_letter" name="cover_letter" rows="6" required
                                      placeholder="Tell us about yourself, your experience, and why you're interested in this position..."></textarea>
                        </div>

                        <div class="form-group full-width">
                            <label for="resume">Resume (PDF, DOC, DOCX - Max 2MB) *</label>
                            <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx" required>
                            <small class="file-info">Please upload your resume in PDF, DOC, or DOCX format (maximum 2MB)</small>
                        </div>

                        <div class="form-group full-width">
                            <div class="g-recaptcha" data-sitekey="6LeMkPwqAAAAAF2mOmW1N1OpEy6en8GGka3YusjX"></div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            Submit Application
                        </button>
                        <button type="button" class="btn btn-secondary modal-close">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/script.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Modal functionality
        const jobDetailsModal = document.getElementById('jobDetailsModal');
        const applicationModal = document.getElementById('applicationModal');
        const modals = [jobDetailsModal, applicationModal];

        // Close modal functionality
        modals.forEach(modal => {
            const closeBtn = modal.querySelector('.close');
            const closeButtons = modal.querySelectorAll('.modal-close');

            closeBtn.onclick = () => modal.style.display = 'none';
            closeButtons.forEach(btn => {
                btn.onclick = () => modal.style.display = 'none';
            });

            window.onclick = (event) => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            };
        });

        // View job details
        document.querySelectorAll('.view-details-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const jobId = this.getAttribute('data-job-id');
                const jobTitle = this.getAttribute('data-job-title') ||
                                this.closest('.job-card').querySelector('h3').textContent;

                const jobDetails = document.getElementById(`job-details-${jobId}`);
                if (jobDetails) {
                    document.getElementById('modalJobTitle').textContent = jobTitle;
                    document.getElementById('modalJobContent').innerHTML = jobDetails.innerHTML;

                    // Set up apply button in modal
                    const modalApplyBtn = document.getElementById('modalApplyBtn');
                    modalApplyBtn.setAttribute('data-job-id', jobId);
                    modalApplyBtn.setAttribute('data-job-title', jobTitle);

                    jobDetailsModal.style.display = 'block';
                }
            });
        });

        // Apply for job
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('apply-btn') || e.target.id === 'modalApplyBtn') {
                const jobId = e.target.getAttribute('data-job-id');
                const jobTitle = e.target.getAttribute('data-job-title');

                // Close job details modal if open
                jobDetailsModal.style.display = 'none';

                // Set up application form
                document.getElementById('applicationJobId').value = jobId;
                document.getElementById('applicationJobTitle').value = jobTitle;
                document.getElementById('preferred_position').value = jobTitle;

                // Show application modal
                applicationModal.style.display = 'block';
            }
        });

        // File upload validation
        document.getElementById('resume').addEventListener('change', function() {
            const file = this.files[0];
            const maxSize = 2 * 1024 * 1024; // 2MB
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

            if (file) {
                if (file.size > maxSize) {
                    alert('File size must be less than 2MB');
                    this.value = '';
                    return;
                }

                if (!allowedTypes.includes(file.type)) {
                    alert('Please upload a PDF, DOC, or DOCX file');
                    this.value = '';
                    return;
                }
            }
        });

        // Form submission handling
        document.getElementById('applicationForm').addEventListener('submit', function(e) {
            // Basic validation
            const name = document.getElementById('applicant_name').value.trim();
            const email = document.getElementById('applicant_email').value.trim();
            const phone = document.getElementById('applicant_phone').value.trim();
            const coverLetter = document.getElementById('cover_letter').value.trim();
            const resume = document.getElementById('resume').files[0];

            if (!name || !email || !phone || !coverLetter || !resume) {
                e.preventDefault();
                alert('Please fill in all required fields and upload your resume.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return;
            }

            // reCAPTCHA validation
            const recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse) {
                e.preventDefault();
                alert('Please complete the reCAPTCHA verification.');
                return;
            }
        });
    </script>
</body>
</html>
