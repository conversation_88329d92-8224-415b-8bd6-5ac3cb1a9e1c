<?php
session_start();
require_once 'includes/auth.php';
require_admin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

require_once '../includes/Database.php';
$db = Database::getInstance();

// Initialize message variables
$message = '';
$messageType = '';

// Handle status update
if (isset($_GET['action']) && $_GET['action'] == 'update_status' && isset($_GET['id']) && isset($_GET['status'])) {
    $id = (int)$_GET['id'];
    $status = $_GET['status'];

    // Validate status
    $valid_statuses = ['new', 'in_progress', 'resolved'];
    if (in_array($status, $valid_statuses)) {
        try {
            $db->update('inquiries', ['status' => $status], "id = $id");
            $message = "Inquiry status updated successfully!";
            $messageType = "success";
        } catch (Exception $e) {
            $message = "Error updating inquiry status: " . $e->getMessage();
            $messageType = "error";
        }
    } else {
        $message = "Invalid status value!";
        $messageType = "error";
    }
}

// Handle delete
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];

    try {
        $db->delete('inquiries', "id = $id");
        $message = "Inquiry deleted successfully!";
        $messageType = "success";
    } catch (Exception $e) {
        $message = "Error deleting inquiry: " . $e->getMessage();
        $messageType = "error";
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Build query
$query = "SELECT * FROM inquiries WHERE 1=1";
$params = [];

if (!empty($status_filter)) {
    $query .= " AND status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($date_filter)) {
    $query .= " AND DATE(created_at) = :date";
    $params[':date'] = $date_filter;
}

if (!empty($search)) {
    $query .= " AND (name LIKE :search OR email LIKE :search OR subject LIKE :search OR message LIKE :search)";
    $params[':search'] = "%$search%";
}

$query .= " ORDER BY created_at DESC";

// Get inquiries
try {
    $inquiries = $db->select($query, $params);
} catch (Exception $e) {
    $message = "Error retrieving inquiries: " . $e->getMessage();
    $messageType = "error";
    $inquiries = [];
}

// Get unique dates for filter
try {
    $dates = $db->select("SELECT DISTINCT DATE(created_at) as inquiry_date FROM inquiries ORDER BY inquiry_date DESC");
} catch (Exception $e) {
    $dates = [];
}

// Determine if we're viewing or listing inquiries
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$inquiryId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get inquiry data for viewing
$inquiryData = [];
if ($action === 'view' && $inquiryId > 0) {
    try {
        $inquiryData = $db->selectOne("SELECT * FROM inquiries WHERE id = ?", [$inquiryId]);

        if (empty($inquiryData)) {
            $message = 'Inquiry not found!';
            $messageType = 'error';
            $action = 'list';
        }
    } catch (Exception $e) {
        $message = "Error retrieving inquiry: " . $e->getMessage();
        $messageType = "error";
        $action = 'list';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Inquiries - Doctors At Door Step</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
            --warning-color: #F59E0B;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--dark-color);
            color: var(--white);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 20px;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: rgba(44, 123, 229, 0.2);
            color: var(--primary-color);
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: absolute;
            bottom: 0;
            width: 100%;
        }

        .logout-btn {
            display: block;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        /* Alert Messages */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        /* Filter Section */
        .filter-section {
            background-color: var(--white);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
        }

        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
        }

        /* Inquiries Table */        .inquiries-table {
            width: 100%;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            overflow: visible;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .inquiries-table table {
            width: 100%;
            border-collapse: collapse;
            overflow: visible;
        }

        .inquiries-table th, .inquiries-table td {
            padding: 16px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            vertical-align: middle;
            position: relative;
            overflow: visible;
        }

        .inquiries-table th {
            background-color: #f8fafc;
            font-weight: 600;
            font-size: 13px;
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .inquiries-table tr:last-child td {
            border-bottom: none;
        }

        .inquiries-table tbody tr {
            transition: all 0.2s ease;
            position: relative;
        }

        .inquiries-table tbody tr:hover {
            background-color: rgba(44, 123, 229, 0.03);
        }        .inquiry-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-new {
            background-color: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-in_progress {
            background-color: rgba(245, 158, 11, 0.1);
            color: #d97706;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-resolved {
            background-color: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        /* Status Dropdown */
        .status-dropdown {
            position: relative;
            display: inline-block;
        }

        .status-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: var(--white);
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-top: 5px;
            overflow: hidden;
        }

        .status-dropdown-content::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 15px;
            width: 12px;
            height: 12px;
            background-color: var(--white);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: none;
            border-right: none;
            transform: rotate(45deg);
        }

        .status-dropdown:hover .status-dropdown-content,
        .status-dropdown.active .status-dropdown-content {
            display: block;
            animation: dropdownFadeIn 0.2s ease-out forwards;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-dropdown-content a {
            color: var(--dark-color);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .status-dropdown-content a:last-child {
            border-bottom: none;
        }

        .status-dropdown-content a:hover {
            background-color: #f8fafc;
            color: var(--primary-color);
        }

        .status-dropdown-content a i {
            width: 16px;
            text-align: center;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-view, .btn-delete {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-view {
            background-color: rgba(44, 123, 229, 0.1);
            color: var(--primary-color);
        }

        .btn-view:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-delete {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .btn-delete:hover {
            background-color: var(--error-color);
            color: var(--white);
        }

        /* Inquiry Detail */
        .inquiry-detail {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            padding: 20px;
        }

        .inquiry-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .inquiry-subject {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .inquiry-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 14px;
            color: var(--secondary-color);
        }

        .inquiry-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .inquiry-content {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .reply-form {
            margin-top: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
            min-height: 150px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-back {
            padding: 10px 20px;
            background-color: var(--light-color);
            color: var(--dark-color);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-back:hover {
            background-color: #e0e0e0;
        }

        .btn-reply {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-reply:hover {
            background-color: var(--dark-color);
        }

        /* Form Helpers */
        .text-muted {
            color: var(--secondary-color);
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }

        .text-center {
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .toggle-sidebar {
                display: block;
            }

            .inquiries-table {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <?php if ($action === 'view'): ?>
                    View Inquiry
                <?php else: ?>
                    Manage Inquiries
                <?php endif; ?>
            </h1>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Filter Section -->
            <div class="filter-section">
                <form action="" method="GET" class="filter-form">
                    <div class="filter-group">
                        <label for="status">Filter by Status</label>
                        <select name="status" id="status">
                            <option value="">All Statuses</option>
                            <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                            <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                            <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date">Filter by Date</label>
                        <select name="date" id="date">
                            <option value="">All Dates</option>
                            <?php foreach ($dates as $date): ?>
                                <option value="<?php echo $date['inquiry_date']; ?>" <?php echo $date_filter === $date['inquiry_date'] ? 'selected' : ''; ?>>
                                    <?php echo date('F j, Y', strtotime($date['inquiry_date'])); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="search">Search</label>
                        <input type="text" name="search" id="search" placeholder="Search by name, email, subject, or message" value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="filter-buttons">
                        <button type="submit" class="btn-reply">Apply Filters</button>
                        <a href="inquiries.php" class="btn-back">Reset</a>
                    </div>
                </form>
            </div>

            <!-- Inquiries List -->
            <div class="inquiries-table">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Subject</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($inquiries)): ?>
                            <tr>
                                <td colspan="6" class="text-center">No inquiries found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($inquiries as $inquiry): ?>
                                <tr>
                                    <td><?php echo $inquiry['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($inquiry['name']); ?></strong><br>
                                        <small><?php echo htmlspecialchars($inquiry['email']); ?></small>
                                        <?php if (!empty($inquiry['phone'])): ?>
                                            <br><small><?php echo htmlspecialchars($inquiry['phone']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($inquiry['subject']); ?></td>
                                    <td><?php echo date('M j, Y', strtotime($inquiry['created_at'])); ?></td>
                                    <td>
                                        <div class="status-dropdown">
                                            <span class="inquiry-status status-<?php echo $inquiry['status']; ?>">
                                                <?php
                                                    $status_text = $inquiry['status'];
                                                    if ($status_text === 'in_progress') {
                                                        $status_text = 'In Progress';
                                                    } else {
                                                        $status_text = ucfirst($status_text);
                                                    }
                                                    echo $status_text;
                                                ?>
                                            </span>
                                            <div class="status-dropdown-content">                                                <a href="inquiries.php?action=update_status&id=<?php echo $inquiry['id']; ?>&status=new">
                                                    <i class="fas fa-plus-circle"></i> Mark as New
                                                </a>
                                                <a href="inquiries.php?action=update_status&id=<?php echo $inquiry['id']; ?>&status=in_progress">
                                                    <i class="fas fa-clock"></i> Mark as In Progress
                                                </a>
                                                <a href="inquiries.php?action=update_status&id=<?php echo $inquiry['id']; ?>&status=resolved">
                                                    <i class="fas fa-check-circle"></i> Mark as Resolved
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="inquiries.php?action=view&id=<?php echo $inquiry['id']; ?>" class="btn-view">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="inquiries.php?action=delete&id=<?php echo $inquiry['id']; ?>" class="btn-delete" onclick="return confirm('Are you sure you want to delete this inquiry?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <!-- Inquiry Detail -->
            <div class="inquiry-detail">
                <div class="inquiry-header">
                    <h2 class="inquiry-subject"><?php echo htmlspecialchars($inquiryData['subject']); ?></h2>
                    <div class="inquiry-meta">
                        <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($inquiryData['name']); ?></span>
                        <span><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($inquiryData['email']); ?></span>
                        <span><i class="fas fa-calendar"></i> <?php echo date('M j, Y H:i', strtotime($inquiryData['created_at'])); ?></span>
                        <span>
                            <i class="fas fa-tag"></i>
                            <span class="inquiry-status status-<?php echo $inquiryData['status']; ?>">
                                <?php
                                    $status_text = $inquiryData['status'];
                                    if ($status_text === 'in_progress') {
                                        $status_text = 'In Progress';
                                    } else {
                                        $status_text = ucfirst($status_text);
                                    }
                                    echo $status_text;
                                ?>
                            </span>
                        </span>
                    </div>
                </div>

                <div class="inquiry-content">
                    <p><?php echo nl2br(htmlspecialchars($inquiryData['message'])); ?></p>
                </div>

                <div class="inquiry-actions">
                    <a href="inquiries.php" class="btn-back">Back to List</a>
                    <div class="status-dropdown">
                        <button class="btn-reply">Change Status</button>
                        <div class="status-dropdown-content">                            <a href="inquiries.php?action=update_status&id=<?php echo $inquiryData['id']; ?>&status=new">
                                <i class="fas fa-plus-circle"></i> Mark as New
                            </a>
                            <a href="inquiries.php?action=update_status&id=<?php echo $inquiryData['id']; ?>&status=in_progress">
                                <i class="fas fa-clock"></i> Mark as In Progress
                            </a>
                            <a href="inquiries.php?action=update_status&id=<?php echo $inquiryData['id']; ?>&status=resolved">
                                <i class="fas fa-check-circle"></i> Mark as Resolved
                            </a>
                        </div>
                    </div>
                    <a href="inquiries.php?action=delete&id=<?php echo $inquiryData['id']; ?>" class="btn-delete" onclick="return confirm('Are you sure you want to delete this inquiry?')">Delete</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });

        // Confirm delete
        function confirmDelete(id, subject) {
            if (confirm(`Are you sure you want to delete the inquiry "${subject}"?`)) {
                window.location.href = `inquiries.php?action=delete&id=${id}`;
            }
        }
    </script>
</body>
</html>
