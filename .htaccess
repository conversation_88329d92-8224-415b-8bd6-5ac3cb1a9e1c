#Doctors at your doorstep - .htaccess File
# Improves security and hides .php extensions in URLs

# Enable the rewrite engine
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Redirect to HTTPS (uncomment in production)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Exclude uploads directory from rewriting
    RewriteRule ^uploads/ - [L]    # Remove trailing slashes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)/$ /$1 [L,R=301]    # Hide .php extension
    # Redirect example.php to example (exclude admin directory entirely)
    RewriteCond %{THE_REQUEST} \s/+([^.]+)\.php\s
    RewriteCond %{REQUEST_URI} !^/admin/
    RewriteRule ^ /%1 [R=301,L]

    # Internally map example to example.php
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}.php -f
    RewriteCond %{REQUEST_URI} !^/admin/
    RewriteRule ^([^.]+)$ $1.php [L]

    # Handle 404 errors
    ErrorDocument 404 /404.php
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    # Protect against XSS attacks
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevent MIME-type sniffing
    Header set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header set X-Frame-Options "SAMEORIGIN"
    
    # Enable Content Security Policy (customize as needed)
    # Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://cdnjs.cloudflare.com; style-src 'self' https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com;"
    
    # HTTP Strict Transport Security (HSTS) - uncomment in production
    # Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Disable server signature
    ServerSignature Off
</IfModule>

# Protect sensitive files
<FilesMatch "^(\.htaccess|\.htpasswd|config\.php|.*\.sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect includes directory
<IfModule mod_rewrite.c>
    RewriteRule ^includes/ - [F,L]
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect against script injections
Options +FollowSymLinks
Options -ExecCGI
AddHandler cgi-script .pl .py .jsp .asp .shtml .sh .cgi

# Set default character set
AddDefaultCharset UTF-8

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType application/json .json
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType image/x-icon .ico
</IfModule>

# Configure caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType application/x-font-ttf "access plus 1 year"
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    # Disallow dangerous PHP functions
    php_admin_value disable_functions "exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source"
    
    # Hide PHP version
    php_flag expose_php Off
    
    # Set maximum upload file size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # Set maximum execution time
    php_value max_execution_time 60
    
    # Set maximum input time
    php_value max_input_time 60
</IfModule>

# Prevent access to .git directory
<IfModule mod_rewrite.c>
    RewriteRule ^(.*/)?\.git/ - [F,L]
</IfModule>

# Prevent access to backup and source files
<FilesMatch "(\.(bak|config|sql|fla|psd|ini|log|sh|inc|swp|dist)|~)$">
    Order Allow,Deny
    Deny from all
    Satisfy All
</FilesMatch>
