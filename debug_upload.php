<?php
require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>Partners Table Setup</h1>";
    echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;}</style>";
    
    // Check if table exists
    $tableExists = $db->select("SHOW TABLES LIKE 'partners'");
    
    if (empty($tableExists)) {
        echo "<p class='error'>Partners table does not exist. Creating it...</p>";
        
        // Create partners table
        $createTableSQL = "
        CREATE TABLE partners (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            logo VARCHAR(500) NOT NULL,
            website_url VARCHAR(500) NULL,
            description TEXT NULL,
            display_order INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($db->query($createTableSQL)) {
            echo "<p class='success'>✓ Partners table created successfully!</p>";
        } else {
            echo "<p class='error'>✗ Failed to create partners table</p>";
        }
    } else {
        echo "<p class='success'>✓ Partners table already exists</p>";
    }
    
    // Show table structure
    $structure = $db->select("DESCRIBE partners");
    echo "<h2>Table Structure:</h2>";
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test insert
    echo "<h2>Test Insert:</h2>";
    $testSQL = "INSERT INTO partners (name, logo, website_url, description, display_order, status) 
                VALUES ('Test Partner', 'test.jpg', 'https://test.com', 'Test description', 1, 'active')";
    
    if ($db->query($testSQL)) {
        echo "<p class='success'>✓ Test insert successful</p>";
        
        // Clean up test data
        $db->query("DELETE FROM partners WHERE name = 'Test Partner'");
        echo "<p>Test data cleaned up</p>";
    } else {
        echo "<p class='error'>✗ Test insert failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
?>
