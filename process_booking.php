<?php
// Ensure no output before JSON response
ob_start();

// Include configuration file
require_once 'includes/config.php';

// Include database connection
require_once 'includes/Database.php';

// Include PHPMailer for email functionality
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON>Mailer\PHPMailer\Exception;

// Configuration
$admin_email = ADMIN_EMAIL; // Use email from config
$log_file = "logs/booking_submissions.log";

// Use the same environment detection as config.php
$http_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
$is_localhost = (
    $http_host === 'localhost:8080' ||
    $http_host === 'localhost' ||
    $http_host === '127.0.0.1' ||
    $http_host === '127.0.0.1:8080' ||
    php_sapi_name() === 'cli' // Command line interface
);

define('DEV_MODE', $is_localhost);

// Error reporting based on environment
if (DEV_MODE) {
    ini_set('display_errors', 0); // Never display errors in AJAX responses
    error_reporting(E_ALL & ~E_WARNING); // Suppress mail warnings in dev
} else {
    ini_set('display_errors', 0);
    error_reporting(E_ERROR | E_PARSE);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/php_errors.log');
}

// Function to send JSON response and exit cleanly
function send_json_response($response) {
    // Clear any output that might have been generated
    ob_clean();

    // Set proper headers
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');

    // Send response
    echo json_encode($response);
    exit;
}

// Test database connection early
try {
    $db = Database::getInstance();
    if (DEV_MODE) {
        error_log("Database connection successful");
    }
} catch (Exception $e) {
    error_log("Database connection failed: " . $e->getMessage());
    if (DEV_MODE) {
        $response = [
            'success' => false,
            'message' => 'Database connection failed: ' . $e->getMessage()
        ];
        send_json_response($response);
    } else {
        // In production, log the error but show a generic message
        $response = [
            'success' => false,
            'message' => 'Service temporarily unavailable. Please try again later.'
        ];
        send_json_response($response);
    }
}

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to generate booking reference
function generate_booking_reference() {
    return 'BK' . date('Ymd') . strtoupper(substr(uniqid(), -6));
}

// Function to log bookings
function log_booking($data) {
    global $log_file;
    $log_entry = date('Y-m-d H:i:s') . " | " .
                 "Ref: " . $data['reference'] . " | " .
                 "Name: " . $data['firstName'] . " " . $data['lastName'] . " | " .
                 "Email: " . $data['email'] . " | " .
                 "Service: " . $data['service'] . " | " .
                 "Date: " . $data['preferredDate'] . "\n";

    // Create logs directory if it doesn't exist
    if (!file_exists(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }

    file_put_contents($log_file, $log_entry, FILE_APPEND);
}

// Function to send confirmation email using PHPMailer
function send_confirmation_email($data) {
    $mail = new PHPMailer(true);

    try {
        // Server settings based on configuration
        if (MAIL_METHOD === 'smtp') {
            $mail->isSMTP();
            $mail->Host       = SMTP_HOST;
            $mail->SMTPAuth   = true;
            $mail->Username   = SMTP_USERNAME;
            $mail->Password   = SMTP_PASSWORD;
            $mail->SMTPSecure = SMTP_SECURE;
            $mail->Port       = SMTP_PORT;
            
            // For debugging in development
            if (DEV_MODE) {
                $mail->SMTPDebug = 0; // Keep quiet for AJAX responses
                error_log("Using SMTP for email delivery");
            }        } elseif (MAIL_METHOD === 'mail') {
            // Use hosting provider's built-in mail() function
            $mail->isMail();
            error_log("Using hosting provider's mail() function for email delivery");
        } else {
            // Log email instead of sending for development - with full details
            $email_log = "logs/email_log.txt";
            if (!file_exists(dirname($email_log))) {
                mkdir(dirname($email_log), 0755, true);
            }

            $log_content = "=== " . date('Y-m-d H:i:s') . " CONFIRMATION EMAIL ===\n";
            $log_content .= "To: " . $data['email'] . "\n";
            $log_content .= "Subject: Booking Confirmation - Reference: " . $data['reference'] . "\n";
            $log_content .= "From: " . SITE_NAME . " <" . NOREPLY_EMAIL . ">\n";
            $log_content .= "Reply-To: " . SUPPORT_EMAIL . "\n\n";
            
            $log_content .= "Dear " . $data['firstName'] . " " . $data['lastName'] . ",\n\n";
            $log_content .= "Thank you for booking with " . SITE_NAME . ". Here are your booking details:\n\n";
            $log_content .= "Booking Reference: " . $data['reference'] . "\n";
            $log_content .= "Service: " . $data['service'] . "\n";
            $log_content .= "Care Type: " . $data['careType'] . "\n";
            $log_content .= "Date: " . $data['preferredDate'] . "\n";
            $log_content .= "Time: " . $data['preferredTime'] . "\n";
            $log_content .= "Duration: " . $data['duration'] . "\n";
            $log_content .= "Medical Condition: " . ($data['medicalCondition'] ?? 'Not specified') . "\n";
            $log_content .= "Special Requirements: " . ($data['specialRequirements'] ?? 'None') . "\n";
            $log_content .= "Emergency Contact: " . $data['emergencyName'] . " (" . $data['relationship'] . ") - " . $data['emergencyPhone'] . "\n\n";
            
            $log_content .= "We will contact you shortly to confirm your appointment.\n\n";
            $log_content .= "If you have any questions, please contact us at " . SUPPORT_EMAIL . ".\n\n";
            $log_content .= "Best regards,\n";
            $log_content .= SITE_NAME . " Team\n";
            $log_content .= "========================================\n\n";

            file_put_contents($email_log, $log_content, FILE_APPEND);
            error_log("Detailed confirmation email logged for development: " . $data['email']);
            return true;
        }

        // Recipients
        $mail->setFrom(NOREPLY_EMAIL, SITE_NAME);
        $mail->addAddress($data['email'], $data['firstName'] . ' ' . $data['lastName']);
        $mail->addReplyTo(SUPPORT_EMAIL, SITE_NAME . ' Support');        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Booking Confirmation - Reference: ' . $data['reference'];
        
        // Use live domain URL for logo in emails
        $logo_url = 'https://doctorsatdoorstep.com.np/images/logo.png';
        
        // Modern HTML email template with logo
        $mail->Body = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Booking Confirmation</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
                .header { background: linear-gradient(135deg, #2C7BE5, #1e5bb8); color: white; padding: 30px 20px; text-align: center; position: relative; }
                .logo { max-width: 120px; height: auto; margin-bottom: 15px; }
                .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
                .header p { margin: 10px 0 0; opacity: 0.9; font-size: 16px; }
                .content { padding: 40px 30px; }
                .greeting { font-size: 18px; margin-bottom: 20px; }
                .intro-text { margin-bottom: 25px; color: #555; }
                .booking-card { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 12px; padding: 25px; margin: 25px 0; border-left: 4px solid #2C7BE5; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }
                .booking-card h3 { margin: 0 0 20px; color: #2C7BE5; font-size: 20px; display: flex; align-items: center; }
                .detail-row { display: flex; justify-content: space-between; margin: 12px 0; padding: 12px 0; border-bottom: 1px solid #dee2e6; }
                .detail-row:last-child { border-bottom: none; }
                .detail-label { font-weight: 600; color: #495057; flex: 1; }
                .detail-value { color: #212529; font-weight: 500; flex: 1; text-align: right; }
                .reference-highlight { background: linear-gradient(135deg, #2C7BE5, #1e5bb8); color: white; padding: 15px 20px; border-radius: 8px; font-weight: bold; font-size: 18px; text-align: center; margin: 25px 0; box-shadow: 0 3px 6px rgba(44, 123, 229, 0.3); }
                .next-steps { background: linear-gradient(135deg, #d1ecf1, #b3d9e6); border: 1px solid #bee5eb; border-radius: 12px; padding: 25px; margin: 25px 0; }
                .next-steps h4 { color: #0c5460; margin: 0 0 15px; font-size: 18px; }
                .next-steps ul { margin: 10px 0; padding-left: 20px; }
                .next-steps li { margin: 8px 0; color: #0c5460; }
                .contact-info { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 12px; padding: 25px; margin: 25px 0; text-align: center; }
                .contact-info h4 { color: #2C7BE5; margin: 0 0 15px; }
                .contact-details { display: flex; justify-content: space-around; flex-wrap: wrap; }
                .contact-item { margin: 10px; }
                .contact-item strong { color: #2C7BE5; }
                .footer { background: #343a40; color: #ffffff; padding: 25px; text-align: center; font-size: 14px; }
                .footer-logo { max-width: 80px; height: auto; margin-bottom: 10px; opacity: 0.8; }
                .social-links { margin: 15px 0; }
                .social-links a { color: #ffffff; text-decoration: none; margin: 0 10px; }
                .divider { height: 1px; background: linear-gradient(90deg, transparent, #dee2e6, transparent); margin: 30px 0; }
                @media (max-width: 600px) {
                    .detail-row { flex-direction: column; }
                    .detail-value { text-align: left; margin-top: 5px; }
                    .contact-details { flex-direction: column; }
                }
            </style>
        </head>
        <body>            <div class='container'>
                <div class='header'>
                    <img src='" . $logo_url . "' alt='" . SITE_NAME . " Logo' class='logo'>
                    <h1>" . SITE_NAME . "</h1>
                    <p>Your booking has been confirmed!</p>
                </div>
                
                <div class='content'>
                    <div class='greeting'>
                        Dear <strong>" . htmlspecialchars($data['firstName'] . " " . $data['lastName']) . "</strong>,
                    </div>
                    
                    <div class='intro-text'>
                        Thank you for choosing " . SITE_NAME . ". We're pleased to confirm that your booking request has been successfully submitted and is now being processed by our medical team.
                    </div>

                    <div class='reference-highlight'>
                        📋 Booking Reference: " . htmlspecialchars($data['reference']) . "
                    </div>

                    <div class='booking-card'>
                        <h3>📅 Appointment Details</h3>
                        <div class='detail-row'>
                            <span class='detail-label'>Service:</span>
                            <span class='detail-value'>" . htmlspecialchars($data['service']) . "</span>
                        </div>
                        <div class='detail-row'>
                            <span class='detail-label'>Care Type:</span>
                            <span class='detail-value'>" . htmlspecialchars($data['careType']) . "</span>
                        </div>
                        <div class='detail-row'>
                            <span class='detail-label'>Preferred Date:</span>
                            <span class='detail-value'>" . htmlspecialchars(date('F j, Y', strtotime($data['preferredDate']))) . "</span>
                        </div>
                        <div class='detail-row'>
                            <span class='detail-label'>Preferred Time:</span>
                            <span class='detail-value'>" . htmlspecialchars(ucfirst($data['preferredTime'])) . "</span>
                        </div>
                        <div class='detail-row'>
                            <span class='detail-label'>Duration:</span>
                            <span class='detail-value'>" . htmlspecialchars($data['duration']) . "</span>
                        </div>
                    </div>                    <div class='next-steps'>
                        <h4>🩺 What happens next?</h4>
                        <ul>
                            <li>Our care coordinator will contact you within <strong>24 hours</strong></li>
                            <li>We'll confirm your appointment details and address any special requirements</li>
                            <li>A qualified healthcare professional will be assigned to your case</li>
                            <li>You'll receive a follow-up confirmation with complete appointment details</li>
                            <li>Our medical team will review your case and prepare for your visit</li>
                        </ul>
                    </div>

                    <div class='divider'></div>

                    <div class='contact-info'>
                        <h4>� Need to reach us?</h4>
                        <div class='contact-details'>
                            <div class='contact-item'>
                                <strong>Support Email:</strong><br>
                                " . SUPPORT_EMAIL . "
                            </div>
                            <div class='contact-item'>
                                <strong>Emergency Line:</strong><br>
                                " . htmlspecialchars($data['phone']) . " Available 24/7
                            </div>
                        </div>
                        
                        <p style='margin-top: 20px; font-style: italic; color: #666;'>
                            Please keep your booking reference number <strong style='color: #2C7BE5;'>" . htmlspecialchars($data['reference']) . "</strong> handy for all communications.
                        </p>
                    </div>

                    <div style='background: linear-gradient(135deg, #e3f2fd, #bbdefb); padding: 20px; border-radius: 12px; margin: 25px 0; text-align: center;'>
                        <p style='margin: 0; color: #1565c0; font-weight: 500;'>
                            🌟 We appreciate your trust in our healthcare services and look forward to providing you with exceptional care at your doorstep.
                        </p>
                    </div>
                    
                    <p style='margin-top: 30px;'>Warm regards,<br>
                    <strong style='color: #2C7BE5;'>The " . SITE_NAME . " Medical Team</strong></p>
                </div>                <div class='footer'>
                    <img src='" . $logo_url . "' alt='" . SITE_NAME . " Logo' class='footer-logo'>
                    <p style='margin: 10px 0;'><strong>" . SITE_NAME . "</strong></p>
                    <p style='margin: 5px 0;'>Professional Healthcare Services at Your Doorstep</p>
                    <div class='social-links'>
                        <a href='mailto:" . SUPPORT_EMAIL . "'>✉️ Email</a>
                        <a href='" . SITE_URL . "'>🌐 Website</a>
                        <a href='" . SITE_URL . "/contact.php'>📞 Contact</a>
                    </div>
                    <div style='margin-top: 20px; padding-top: 15px; border-top: 1px solid #495057;'>
                        <p style='margin: 5px 0; font-size: 12px; opacity: 0.8;'>This is an automated confirmation email. Please do not reply directly.</p>
                        <p style='margin: 5px 0; font-size: 12px; opacity: 0.8;'>If you did not make this booking, please contact us immediately at " . SUPPORT_EMAIL . "</p>
                        <p style='margin: 15px 0 5px; font-size: 12px; opacity: 0.7;'>&copy; " . date('Y') . " " . SITE_NAME . ". All rights reserved.</p>
                    </div>
                </div>
            </div>
        </body>
        </html>";

        // Plain text version
        $mail->AltBody = "Dear " . $data['firstName'] . " " . $data['lastName'] . ",\n\n";
        $mail->AltBody .= "Your booking with " . SITE_NAME . " has been confirmed!\n\n";
        $mail->AltBody .= "BOOKING DETAILS:\n";
        $mail->AltBody .= "Reference: " . $data['reference'] . "\n";
        $mail->AltBody .= "Service: " . $data['service'] . "\n";
        $mail->AltBody .= "Care Type: " . $data['careType'] . "\n";
        $mail->AltBody .= "Date: " . date('F j, Y', strtotime($data['preferredDate'])) . "\n";
        $mail->AltBody .= "Time: " . $data['preferredTime'] . "\n";
        $mail->AltBody .= "Duration: " . $data['duration'] . "\n\n";
        $mail->AltBody .= "Our team will contact you within 24 hours at " . $data['phone'] . " to confirm details.\n\n";
        $mail->AltBody .= "For questions, contact us at " . SUPPORT_EMAIL . "\n\n";
        $mail->AltBody .= "Best regards,\n" . SITE_NAME . " Team";

        $mail->send();
        
        if (DEV_MODE) {
            error_log("Confirmation email sent successfully to: " . $data['email']);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Email sending failed: " . $mail->ErrorInfo);
        
        if (DEV_MODE) {
            error_log("PHPMailer Error: " . $e->getMessage());
        }
        
        return false;
    }
}

// Function to send admin notification
function send_admin_notification($data) {
    global $admin_email;

    $subject = "New Booking Request - " . $data['reference'];

    // Create HTML message
    $html_message = "
    <html>
    <head>
        <title>New Booking Request</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2C7BE5; color: white; padding: 15px; text-align: center; }
            .content { padding: 20px; border: 1px solid #ddd; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #777; }
            .booking-details { background-color: #f9f9f9; padding: 15px; margin: 15px 0; }
            .section { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
            .reference { font-weight: bold; color: #2C7BE5; }
            table { width: 100%; border-collapse: collapse; }
            table td { padding: 8px; border-bottom: 1px solid #eee; }
            table td:first-child { font-weight: bold; width: 40%; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>New Booking Request</h2>
            </div>
            <div class='content'>
                <p>A new booking request has been received with reference: <span class='reference'>" . htmlspecialchars($data['reference']) . "</span></p>

                <div class='section'>
                    <h3>Patient Information</h3>
                    <table>
                        <tr>
                            <td>Name:</td>
                            <td>" . htmlspecialchars($data['firstName'] . " " . $data['lastName']) . "</td>
                        </tr>
                        <tr>
                            <td>Email:</td>
                            <td>" . htmlspecialchars($data['email']) . "</td>
                        </tr>
                        <tr>
                            <td>Phone:</td>
                            <td>" . htmlspecialchars($data['phone']) . "</td>
                        </tr>
                    </table>
                </div>

                <div class='section'>
                    <h3>Service Details</h3>
                    <table>
                        <tr>
                            <td>Service:</td>
                            <td>" . htmlspecialchars($data['service']) . "</td>
                        </tr>
                        <tr>
                            <td>Care Type:</td>
                            <td>" . htmlspecialchars($data['careType']) . "</td>
                        </tr>
                        <tr>
                            <td>Date:</td>
                            <td>" . htmlspecialchars($data['preferredDate']) . "</td>
                        </tr>
                        <tr>
                            <td>Time:</td>
                            <td>" . htmlspecialchars($data['preferredTime']) . "</td>
                        </tr>
                        <tr>
                            <td>Duration:</td>
                            <td>" . htmlspecialchars($data['duration']) . "</td>
                        </tr>
                    </table>
                </div>

                <div class='section'>
                    <h3>Medical Information</h3>
                    <p><strong>Medical Condition:</strong></p>
                    <p>" . (empty($data['medicalCondition']) ? "None specified" : nl2br(htmlspecialchars($data['medicalCondition']))) . "</p>

                    <p><strong>Special Requirements:</strong></p>
                    <p>" . (empty($data['specialRequirements']) ? "None specified" : nl2br(htmlspecialchars($data['specialRequirements']))) . "</p>
                </div>

                <div class='section'>
                    <h3>Emergency Contact</h3>
                    <table>
                        <tr>
                            <td>Name:</td>
                            <td>" . htmlspecialchars($data['emergencyName']) . "</td>
                        </tr>
                        <tr>
                            <td>Phone:</td>
                            <td>" . htmlspecialchars($data['emergencyPhone']) . "</td>
                        </tr>
                        <tr>
                            <td>Relationship:</td>
                            <td>" . htmlspecialchars($data['relationship']) . "</td>
                        </tr>
                    </table>
                </div>

                <p>Please log in to the admin dashboard to manage this booking.</p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the Doctors At Door Step booking system.</p>
            </div>
        </div>
    </body>
    </html>";

    // Plain text alternative
    $text_message = "New booking request received:\n\n";
    $text_message .= "Reference: " . $data['reference'] . "\n\n";

    $text_message .= "PATIENT INFORMATION\n";
    $text_message .= "Name: " . $data['firstName'] . " " . $data['lastName'] . "\n";
    $text_message .= "Email: " . $data['email'] . "\n";
    $text_message .= "Phone: " . $data['phone'] . "\n\n";

    $text_message .= "SERVICE DETAILS\n";
    $text_message .= "Service: " . $data['service'] . "\n";
    $text_message .= "Care Type: " . $data['careType'] . "\n";
    $text_message .= "Date: " . $data['preferredDate'] . "\n";
    $text_message .= "Time: " . $data['preferredTime'] . "\n";
    $text_message .= "Duration: " . $data['duration'] . "\n\n";

    $text_message .= "MEDICAL INFORMATION\n";
    $text_message .= "Medical Condition:\n" . $data['medicalCondition'] . "\n\n";
    $text_message .= "Special Requirements:\n" . $data['specialRequirements'] . "\n\n";

    $text_message .= "EMERGENCY CONTACT\n";
    $text_message .= "Name: " . $data['emergencyName'] . "\n";
    $text_message .= "Phone: " . $data['emergencyPhone'] . "\n";
    $text_message .= "Relationship: " . $data['relationship'] . "\n\n";

    $text_message .= "Please log in to the admin dashboard to manage this booking.";

    // Create a boundary for the email
    $boundary = md5(time());

    // Headers
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "From: Doctors At Door Step System <" . NOREPLY_EMAIL . ">\r\n";
    $headers .= "Reply-To: " . $data['email'] . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $headers .= "Content-Type: multipart/alternative; boundary=\"" . $boundary . "\"\r\n";

    // Email body
    $body = "--" . $boundary . "\r\n";
    $body .= "Content-Type: text/plain; charset=UTF-8\r\n";
    $body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
    $body .= $text_message . "\r\n\r\n";

    $body .= "--" . $boundary . "\r\n";
    $body .= "Content-Type: text/html; charset=UTF-8\r\n";
    $body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
    $body .= $html_message . "\r\n\r\n";

    $body .= "--" . $boundary . "--";

    // In development mode, just log the email instead of trying to send it
    if (DEV_MODE) {
        // Log the email to a file for debugging
        $email_log = "logs/admin_email_log.txt";
        if (!file_exists(dirname($email_log))) {
            mkdir(dirname($email_log), 0755, true);
        }

        $log_content = "=== " . date('Y-m-d H:i:s') . " ===\n";
        $log_content .= "To: " . $admin_email . "\n";
        $log_content .= "Subject: " . $subject . "\n";
        $log_content .= "Headers: " . $headers . "\n";
        $log_content .= "Body: " . $text_message . "\n\n";

        file_put_contents($email_log, $log_content, FILE_APPEND);

        // In development mode, we'll consider this a success
        return true;
    } else {
        // In production, actually try to send the email
        return mail($admin_email, $subject, $body, $headers);
    }
}

// Function to send booking alert via Formspree
function send_formspree_booking_alert($data) {
    $formspree_url = 'https://formspree.io/f/xqabgezq'; // Same Formspree ID as contact form
    
    try {
        // Prepare the data for Formspree
        $formspree_data = [
            'name' => $data['firstName'] . ' ' . $data['lastName'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'subject' => 'New Booking Alert - ' . $data['reference'],
            'message' => "📋 NEW BOOKING ALERT\n\n" .
                        "Booking Reference: " . $data['reference'] . "\n" .
                        "Patient Name: " . $data['firstName'] . " " . $data['lastName'] . "\n" .
                        "Email: " . $data['email'] . "\n" .
                        "Phone: " . $data['phone'] . "\n" .
                        "Service: " . $data['service'] . "\n" .
                        "Care Type: " . $data['careType'] . "\n" .
                        "Preferred Date: " . date('F j, Y', strtotime($data['preferredDate'])) . "\n" .
                        "Preferred Time: " . ucfirst($data['preferredTime']) . "\n" .
                        "Duration: " . $data['duration'] . "\n" .
                        "Medical Condition: " . ($data['medicalCondition'] ?? 'Not specified') . "\n" .
                        "Special Requirements: " . ($data['specialRequirements'] ?? 'None') . "\n" .
                        "Emergency Contact: " . $data['emergencyName'] . " (" . $data['relationship'] . ") - " . $data['emergencyPhone'] . "\n\n" .
                        "Booking submitted at: " . date('Y-m-d H:i:s T') . "\n" .
                        "Source: " . SITE_NAME . " Booking System",
            '_replyto' => $data['email'],
            '_subject' => 'New Booking Alert - ' . $data['reference'],
            '_next' => SITE_URL // Redirect after submission (not used in our case)
        ];

        // Initialize cURL
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $formspree_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($formspree_data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'User-Agent: ' . SITE_NAME . ' Booking System'
            ]
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        // Check for cURL errors
        if ($curl_error) {
            error_log("Formspree cURL Error: " . $curl_error);
            return false;
        }
        
        // Check HTTP response code
        if ($http_code >= 200 && $http_code < 300) {
            if (DEV_MODE) {
                error_log("Formspree booking alert sent successfully. HTTP Code: " . $http_code);
                error_log("Formspree Response: " . $response);
            }
            return true;
        } else {
            error_log("Formspree booking alert failed. HTTP Code: " . $http_code . " Response: " . $response);
            return false;
        }
        
    } catch (Exception $e) {
        error_log("Formspree booking alert error: " . $e->getMessage());
        return false;
    }
}

// Process booking submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $response = [
        'success' => false,
        'message' => '',
        'reference' => ''
    ];

    // Validate required fields
    $required_fields = [
        'firstName', 'lastName', 'email', 'phone',
        'service', 'careType', 'preferredDate', 'preferredTime',
        'duration', 'emergencyName', 'emergencyPhone', 'relationship'
    ];

    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = ucfirst($field);
        }
    }

    if (!empty($missing_fields)) {
        $response['message'] = "Please fill in the following fields: " . implode(', ', $missing_fields);
        send_json_response($response);
    }    // Sanitize input
    $booking_data = [
        'reference' => !empty($_POST['bookingReference']) ? sanitize_input($_POST['bookingReference']) : generate_booking_reference(),
        'firstName' => sanitize_input($_POST['firstName']),
        'lastName' => sanitize_input($_POST['lastName']),
        'email' => sanitize_input($_POST['email']),
        'phone' => sanitize_input($_POST['phone']),
        'service' => sanitize_input($_POST['service']),
        'careType' => sanitize_input($_POST['careType']),
        'preferredDate' => sanitize_input($_POST['preferredDate']),
        'preferredTime' => sanitize_input($_POST['preferredTime']),
        'duration' => sanitize_input($_POST['duration']),
        'medicalCondition' => sanitize_input($_POST['medicalCondition'] ?? ''),
        'specialRequirements' => sanitize_input($_POST['specialRequirements'] ?? ''),
        'emergencyName' => sanitize_input($_POST['emergencyName']),
        'emergencyPhone' => sanitize_input($_POST['emergencyPhone']),
        'relationship' => sanitize_input($_POST['relationship']),
        'submissionType' => sanitize_input($_POST['submissionType'] ?? 'direct') // Track submission type
    ];

    // Validate email
    if (!filter_var($booking_data['email'], FILTER_VALIDATE_EMAIL)) {
        $response['message'] = "Please enter a valid email address.";
        send_json_response($response);
    }

    try {
        // Log booking first (this should always work)
        log_booking($booking_data);

        if (DEV_MODE) {
            error_log("Booking logged successfully for reference: " . $booking_data['reference']);
        }

        // Send confirmation email
        $email_sent = send_confirmation_email($booking_data);

        if (DEV_MODE) {
            error_log("Email sent status: " . ($email_sent ? 'success' : 'failed'));
        }        // Send admin notification
        $admin_notified = send_admin_notification($booking_data);

        if (DEV_MODE) {
            error_log("Admin notification status: " . ($admin_notified ? 'success' : 'failed'));
        }        // Send Formspree booking alert
        $formspree_sent = send_formspree_booking_alert($booking_data);

        if (DEV_MODE) {
            error_log("Formspree booking alert status: " . ($formspree_sent ? 'success' : 'failed'));
        }

        // Store booking in database - this is critical for backend management
        $database_success = false;
        $insert_id = 0;
        
        try {
            // Map the form data to database fields
            $db_booking_data = [
                'reference' => $booking_data['reference'],
                'client_name' => $booking_data['firstName'] . ' ' . $booking_data['lastName'],
                'client_email' => $booking_data['email'],
                'client_phone' => $booking_data['phone'],
                'service_id' => null, // Set to null since we're not using service IDs from the form
                'booking_date' => $booking_data['preferredDate'],
                'booking_time' => ($booking_data['preferredTime'] === 'morning') ? '09:00:00' :
                                 (($booking_data['preferredTime'] === 'afternoon') ? '13:00:00' : '17:00:00'),
                'address' => 'To be confirmed during call', // Default address since it's required
                'special_requests' => "Service: " . $booking_data['service'] . "\n" .
                                     "Care Type: " . $booking_data['careType'] . "\n" .
                                     "Duration: " . $booking_data['duration'] . "\n\n" .
                                     "Medical Condition: " . $booking_data['medicalCondition'] . "\n\n" .
                                     "Special Requirements: " . $booking_data['specialRequirements'] . "\n\n" .
                                     "Emergency Contact: " . $booking_data['emergencyName'] . " (" .
                                     $booking_data['relationship'] . ") - " . $booking_data['emergencyPhone'],
                'status' => 'pending'
            ];

            if (DEV_MODE) {
                error_log("Attempting database insert with data: " . print_r($db_booking_data, true));
            }

            $insert_id = $db->insert('bookings', $db_booking_data);
            
            if ($insert_id > 0) {
                $database_success = true;
                if (DEV_MODE) {
                    error_log("Database insert successful with ID: " . $insert_id);
                }
            } else {
                throw new Exception("Database insert returned false or 0");
            }
            
        } catch (Exception $e) {
            error_log("CRITICAL: Database error during booking: " . $e->getMessage());
            error_log("Booking data: " . print_r($db_booking_data, true));
            
            if (DEV_MODE) {
                error_log("Database insert failed: " . $e->getTraceAsString());
            }
            
            // In production, we might want to fail the booking if database fails
            if (!DEV_MODE) {
                $response['success'] = false;
                $response['message'] = 'Sorry, there was a problem processing your booking. Please try again or contact support.';
                $response['debug'] = 'Database insertion failed';
                send_json_response($response);
            }
        }// Always return success if we've logged the booking and stored it in database
        $response['success'] = true;
        $response['reference'] = $booking_data['reference'];

        if (DEV_MODE) {            // In development mode, provide more detailed information
            $response['message'] = "Your booking has been successfully submitted and stored. Reference: " . $booking_data['reference'];
            $response['dev_info'] = [
                'mode' => 'Development',
                'submission_type' => $booking_data['submissionType'],
                'database_success' => $database_success,
                'database_id' => $insert_id,
                'email_sent' => $email_sent,
                'admin_notified' => $admin_notified,
                'formspree_alert_sent' => $formspree_sent,
                'email_logs' => [
                    'customer_email' => 'logs/email_log.txt',
                    'admin_email' => 'logs/admin_email_log.txt'
                ],
                'booking_log' => $log_file,
                'note' => $booking_data['submissionType'] === 'booking' ? 'This is a secondary submission (Formspree primary)' : 'This is a direct submission'
            ];
        } else {
            // Standard message for production
            if ($database_success && $email_sent) {
                $response['message'] = "Your booking has been successfully submitted! We will contact you shortly to confirm your appointment.";
            } else if ($database_success) {
                $response['message'] = "Your booking has been successfully submitted. We will contact you shortly to confirm your appointment. Please save your reference number: " . $booking_data['reference'];
            } else {
                $response['message'] = "Your booking request has been received. Please save your reference number: " . $booking_data['reference'] . ". We will contact you shortly.";
            }
        }

    } catch (Exception $e) {
        $response['message'] = "An error occurred while processing your booking. Please try again later.";
        error_log("Critical booking error: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());

        if (DEV_MODE) {
            $response['debug_error'] = $e->getMessage();
            $response['debug_trace'] = $e->getTraceAsString();
        }
    }

    // Send JSON response
    send_json_response($response);
}

// If accessed directly without POST data
header("Location: booking.php");
exit;
?>
