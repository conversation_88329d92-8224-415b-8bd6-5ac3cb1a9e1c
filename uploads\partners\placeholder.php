<?php
// Generate a placeholder image for partners
header('Content-Type: image/svg+xml');
?>
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="100" viewBox="0 0 200 100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="100" fill="url(#grad1)" stroke="#dee2e6" stroke-width="2" rx="8"/>
  <text x="100" y="35" font-family="Arial, sans-serif" font-size="14" fill="#6c757d" text-anchor="middle" font-weight="600">PARTNER</text>
  <text x="100" y="55" font-family="Arial, sans-serif" font-size="12" fill="#6c757d" text-anchor="middle">LOGO</text>
  <circle cx="100" cy="75" r="3" fill="#6c757d" opacity="0.5"/>
</svg>
