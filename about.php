<?php 
include 'includes/header.php';
require_once 'includes/Database.php';
require_once 'admin/includes/Team.php';

$db = Database::getInstance();
$pdo = $db->getConnection();
$team = new Team($pdo);
$teamMembers = $team->getAllActiveMembers();
?>

<!-- Hero Banner -->
<section class="page-hero about-hero" data-aos="fade-up">
    <div class="container">
        <div class="breadcrumb">
            <a href="index.php">Home</a>
            <span class="separator">/</span>
            <span>About Us</span>
        </div>
        <h1>Bringing Hospital Standards to Your Home.</h1>
        <p>Dedicated to providing exceptional healthcare with doctors at your doorstep</p>
    </div>
    <div class="scroll-hint">
        <span>Scroll to explore</span>
        <i class="fas fa-chevron-down"></i>
    </div>
</section>

<!-- Our Story -->
<section class="our-story" data-aos="fade-up">
    <div class="container">
        <div class="story-grid">
            <div class="story-content">
                <h2>Our Story</h2>
                <div class="story-image mobile-story-image" data-aos="fade-left">
                    <img src="images/logo.webp" alt="Doctors At Door Step's Journey" loading="lazy">
                </div>
                <div class="story-text">
                <p>Founded in 2020, Doctor at Doorstep Nepal began with a simple mission: to bring quality health care directly to homes, especially for families separated by distance. With more than 5,000 patients served across Nepal, our dedicated team of medical professionals has become a trusted name in home health care services.</p>
                <p>In today’s society, many Nepali families live abroad, leaving elderly parents and loved ones behind. Understanding this growing need, we stepped in to provide reliable, compassionate, and professional home-based medical care. Whether it's doctor visits, nursing care, physiotherapy, or wellness checks, our focus is always on delivering the best care at the comfort of home.</p>
                <p>We are not just a service; we are a commitment to peace of mind. By blending experience, empathy, and innovation, we’re helping redefine how healthcare is delivered in Nepal.</p>
                <p>Our vision is clear — to become the No. 1 home health care provider in Nepal, serving the emotional and medical needs of families who may be apart, but still deeply connected.</p>
                <p>At Doctor at Doorstep Nepal, we bring care that feels like family, because your loved ones deserve nothing less.</p>
                </div>
            </div>
            <div class="story-image desktop-story-image" data-aos="fade-left">
                <img src="images/logo.webp" alt="Doctors At Door Step's Journey" loading="lazy">
            </div>
        </div>
    </div>
</section>

<!-- Mission & Vision -->
<section class="mission-vision" data-aos="fade-up">
    <div class="container">
        <div class="mission-grid">
            <div class="mission-box" data-aos="fade-right">
                <h2>Our Mission</h2>
                <p>To enhance the quality of life for our patients through personalized, professional medical services delivered with compassion and excellence by qualified doctors in the comfort of their homes.</p>
                <div class="mission-icon">
                    <i class="fas fa-heart-pulse"></i>
                </div>
            </div>
            <div class="mission-box" data-aos="fade-left">
                <h2>Our Vision</h2>
                <p>To be the leading provider of doorstep medical services in Nepal, setting the standard for quality care, innovation, and patient satisfaction in the industry.</p>
                <div class="mission-icon">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>

        <!-- Core Values -->
        <div class="values-grid">
            <h2>Our Core Values</h2>
            <div class="values-list">
                <div class="value-item" data-aos="fade-up">
                    <i class="fas fa-heart"></i>
                    <h3>Compassion</h3>
                    <p>Treating each patient with kindness, empathy, and respect</p>
                </div>
                <div class="value-item" data-aos="fade-up" data-aos-delay="100">
                    <i class="fas fa-award"></i>
                    <h3>Excellence</h3>
                    <p>Striving for the highest standards in healthcare delivery</p>
                </div>
                <div class="value-item" data-aos="fade-up" data-aos-delay="200">
                    <i class="fas fa-users"></i>
                    <h3>Teamwork</h3>
                    <p>Collaborating to provide comprehensive care solutions</p>
                </div>
                <div class="value-item" data-aos="fade-up" data-aos-delay="300">
                    <i class="fas fa-shield-heart"></i>
                    <h3>Integrity</h3>
                    <p>Maintaining honesty and transparency in all interactions</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Meet the Team -->
<section class="team-section" data-aos="fade-up">
    <div class="container">
        <h2>Meet Our Team</h2>

        
        <div class="team-grid">
            <?php if (empty($teamMembers)): ?>
                <p class="text-center">No team members found.</p>
            <?php else: ?>
                <?php foreach ($teamMembers as $member): ?>
                    <div class="team-member">
                        <div class="member-image">
                            <?php if ($member['photo_path']): ?>
                                <?php error_log("Image path: " . $member['photo_path']); ?>
                                <img src="<?php echo htmlspecialchars($member['photo_path']); ?>" ...>
                            <?php else: ?>
                                <img src="https://via.placeholder.com/300x400.jpg?text=<?php echo urlencode($member['name']); ?>" 
                                     alt="<?php echo htmlspecialchars($member['name']); ?>" 
                                     loading="lazy">
                            <?php endif; ?>
                        </div>
                        <div class="member-info">
                            <h3><?php echo htmlspecialchars($member['name']); ?></h3>
                            <p class="member-role"><?php echo htmlspecialchars($member['position']); ?></p>
                            <?php if ($member['qualifications']): ?>
                                <p class="member-qualifications"><?php echo htmlspecialchars($member['qualifications']); ?></p>
                            <?php endif; ?>
                            <p class="member-bio"><?php echo htmlspecialchars($member['bio']); ?></p>
                            <?php if ($member['specialties']): ?>
                                <p class="member-specialties">Specialties: <?php echo htmlspecialchars($member['specialties']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Our Approach -->
<section class="approach-section" data-aos="fade-up">
    <div class="container">
        <h2>Our Approach</h2>
        <p class="section-subtitle">A comprehensive, patient-centered care process</p>
        
        <div class="approach-steps">
            <!-- Step 1 -->
            <div class="approach-step" data-aos="fade-right">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h3>Initial Consultation</h3>
                    <p>We begin with a thorough assessment of your needs and create a personalized care plan.</p>
                </div>
            </div>
            <!-- Step 2 -->
            <div class="approach-step" data-aos="fade-right" data-aos-delay="100">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h3>Care Team Assignment</h3>
                    <p>We match you with healthcare professionals who best suit your specific needs.</p>
                </div>
            </div>
            <!-- Step 3 -->
            <div class="approach-step" data-aos="fade-right" data-aos-delay="200">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h3>Care Implementation</h3>
                    <p>Our team delivers comprehensive care following your personalized plan.</p>
                </div>
            </div>
            <!-- Step 4 -->
            <div class="approach-step" data-aos="fade-right" data-aos-delay="300">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h3>Continuous Monitoring</h3>
                    <p>We regularly assess and adjust your care plan to ensure optimal results.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get testimonials data with fixed image paths
require_once 'includes/Testimonials.php';
$testimonialHandler = new Testimonials();
$testimonials = $testimonialHandler->getAllActiveTestimonialsForDisplay();
?>

<!-- Testimonials Section -->
<section class="testimonials">
    <div class="container">
        <div class="section-header">
            <h2>What Our Clients Say</h2>
            <p>Real experiences from families we've helped</p>
        </div>

        <div class="testimonials-slider">
            <!-- Swiper Container -->
            <div class="swiper-container testimonials-swiper">
                <div class="swiper-wrapper">
                    <?php if (empty($testimonials)): ?>
                        <!-- Fallback testimonials with full content -->
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"The care and attention provided by the doctors was exceptional. They made my recovery process so much more comfortable in my own home. The professional approach and genuine concern for my wellbeing really stood out."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Raj Thapa</h4>
                                        <p>Patient</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"Professional medical services at home made all the difference for our elderly father. The doctors were punctual, caring, and highly skilled. We couldn't have asked for better healthcare support."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Sita Sharma</h4>
                                        <p>Family Member</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($testimonials as $testimonial): ?>
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-content">
                                        <p>"<?php echo htmlspecialchars($testimonial['display_content']); ?>"</p>
                                    </div>
                                    <div class="testimonial-author">
                                        <!-- Enhanced image path handling for webserver compatibility -->
                                        <?php if (!empty($testimonial['photo_path'])): ?>
                                            <div class="testimonial-author-image" style="background-image: url('<?php echo htmlspecialchars($testimonial['photo_path']); ?>'); background-size: cover; background-position: center;"></div>
                                        <?php else: ?>
                                            <div class="testimonial-author-image no-image">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="author-info">
                                            <h4><?php echo htmlspecialchars($testimonial['name']); ?></h4>
                                            <p><?php echo htmlspecialchars($testimonial['position']); ?></p>
                                            <?php if ($testimonial['rating'] > 0): ?>
                                            <div class="testimonial-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $testimonial['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
            </div>

            <!-- Add Navigation -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </div>
</section>

<!-- Contact CTA -->
<section class="contact-cta" data-aos="fade-up">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Learn More?</h2>
            <p>Contact us today to schedule a consultation and discover how we can help.</p>
            <div class="cta-buttons">
                <a href="contact.php" class="btn btn-primary">Contact Us</a>
                <a href="booking.php" class="btn btn-outline"style="color:white;">Book Consultation</a>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Slider Initialization Script -->
<script>
// Testimonials Slider Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize testimonials slider
    if (document.querySelector('.testimonials-swiper')) {
        // Check if Swiper is already loaded
        if (typeof Swiper !== 'undefined') {
            initTestimonialsSlider();
        } else {
            // If Swiper isn't loaded yet, wait for a moment and try again
            setTimeout(function() {
                if (typeof Swiper !== 'undefined') {
                    initTestimonialsSlider();
                } else {
                    console.error('Swiper library not loaded properly');
                }
            }, 1000);
        }
    }
});

function initTestimonialsSlider() {
    // Simple configuration without complex animations
    var testimonialSwiper = new Swiper('.testimonials-swiper', {
        // Optional parameters
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        // Responsive breakpoints
        breakpoints: {
            // when window width is >= 768px
            768: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            // when window width is >= 1024px
            1024: {
                slidesPerView: 2,
                spaceBetween: 30
            }
        },
        // Navigation arrows
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        // Pagination
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
