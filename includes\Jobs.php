<?php
require_once 'Database.php';

class Jobs {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all jobs with optional filtering
     */
    public function getAllJobs($active_only = false, $limit = null) {
        try {
            $sql = "SELECT * FROM jobs";
            $params = [];
            
            if ($active_only) {
                $sql .= " WHERE is_active = 1";
            }
            
            $sql .= " ORDER BY created_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }
            
            $stmt = $this->db->prepare($sql);
            if (!empty($params)) {
                $stmt->execute($params);
            } else {
                $stmt->execute();
            }
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching jobs: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a single job by ID
     */
    public function getJobById($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM jobs WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching job: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new job
     */
    public function createJob($data) {
        try {
            $sql = "INSERT INTO jobs (title, description, requirements, responsibilities, location, job_type, experience_level, salary_range, application_deadline, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['title'],
                $data['description'],
                $data['requirements'] ?? null,
                $data['responsibilities'] ?? null,
                $data['location'] ?? null,
                $data['job_type'] ?? 'full_time',
                $data['experience_level'] ?? 'entry',
                $data['salary_range'] ?? null,
                $data['application_deadline'] ?? null,
                $data['is_active'] ?? 1
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error creating job: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an existing job
     */
    public function updateJob($id, $data) {
        try {
            $sql = "UPDATE jobs SET 
                    title = ?, 
                    description = ?, 
                    requirements = ?, 
                    responsibilities = ?, 
                    location = ?, 
                    job_type = ?, 
                    experience_level = ?, 
                    salary_range = ?, 
                    application_deadline = ?, 
                    is_active = ?,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $data['title'],
                $data['description'],
                $data['requirements'] ?? null,
                $data['responsibilities'] ?? null,
                $data['location'] ?? null,
                $data['job_type'] ?? 'full_time',
                $data['experience_level'] ?? 'entry',
                $data['salary_range'] ?? null,
                $data['application_deadline'] ?? null,
                $data['is_active'] ?? 1,
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Error updating job: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a job
     */
    public function deleteJob($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM jobs WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Error deleting job: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Toggle job active status
     */
    public function toggleJobStatus($id) {
        try {
            $stmt = $this->db->prepare("UPDATE jobs SET is_active = NOT is_active WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Error toggling job status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get job statistics
     */
    public function getJobStats() {
        try {
            $stats = [];
            
            // Total jobs
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM jobs");
            $stmt->execute();
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Active jobs
            $stmt = $this->db->prepare("SELECT COUNT(*) as active FROM jobs WHERE is_active = 1");
            $stmt->execute();
            $stats['active'] = $stmt->fetch(PDO::FETCH_ASSOC)['active'];
            
            // Jobs by type
            $stmt = $this->db->prepare("SELECT job_type, COUNT(*) as count FROM jobs WHERE is_active = 1 GROUP BY job_type");
            $stmt->execute();
            $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
        } catch (PDOException $e) {
            error_log("Error fetching job stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Submit a job application
     */
    public function submitApplication($data) {
        try {
            $sql = "INSERT INTO job_applications (job_id, applicant_name, applicant_email, applicant_phone, cover_letter, resume_filename, resume_path) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['job_id'],
                $data['applicant_name'],
                $data['applicant_email'],
                $data['applicant_phone'],
                $data['cover_letter'] ?? null,
                $data['resume_filename'] ?? null,
                $data['resume_path'] ?? null
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error submitting application: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get applications for a job
     */
    public function getJobApplications($job_id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM job_applications WHERE job_id = ? ORDER BY created_at DESC");
            $stmt->execute([$job_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching applications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all applications with job details
     */
    public function getAllApplications() {
        try {
            $sql = "SELECT ja.*, j.title as job_title, j.location as job_location 
                    FROM job_applications ja 
                    JOIN jobs j ON ja.job_id = j.id 
                    ORDER BY ja.created_at DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching all applications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update application status
     */
    public function updateApplicationStatus($id, $status, $notes = null) {
        try {
            $sql = "UPDATE job_applications SET status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$status, $notes, $id]);
        } catch (PDOException $e) {
            error_log("Error updating application status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get application statistics
     */
    public function getApplicationStats() {
        try {
            $stats = [];
            
            // Total applications
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM job_applications");
            $stmt->execute();
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Applications by status
            $stmt = $this->db->prepare("SELECT status, COUNT(*) as count FROM job_applications GROUP BY status");
            $stmt->execute();
            $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Recent applications (last 30 days)
            $stmt = $this->db->prepare("SELECT COUNT(*) as recent FROM job_applications WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
            $stmt->execute();
            $stats['recent'] = $stmt->fetch(PDO::FETCH_ASSOC)['recent'];
            
            return $stats;
        } catch (PDOException $e) {
            error_log("Error fetching application stats: " . $e->getMessage());
            return [];
        }
    }
}
?>
