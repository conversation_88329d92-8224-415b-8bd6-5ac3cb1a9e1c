/* Partners Section Styles */
.partners-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.partners-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.partners-section .section-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.partners-section .section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
    position: relative;
}

.partners-section .section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    border-radius: 2px;
}

.partners-section .section-header p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Logo Slider Container */
.partners-slider-container {
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    max-width: 1400px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 40px 0;
}

/* Infinite Slider Track */
.partners-slider-track {
    display: flex;
    animation: slide 60s linear infinite;
    width: max-content;
}

.partners-slider-track:hover {
    animation-play-state: paused;
}

/* Individual Partner Logo */
.partner-logo {
    flex: 0 0 200px;
    height: 120px;
    margin: 0 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.partner-logo:hover {
    transform: translateY(-10px) scale(1.05);
}

.partner-logo img {
    max-width: 150px;
    max-height: 80px;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(1) opacity(0.7);
    transition: all 0.3s ease;
    border-radius: 8px;
}

.partner-logo:hover img {
    filter: grayscale(0) opacity(1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Partner Name Tooltip */
.partner-logo::after {
    content: attr(data-name);
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: #2c3e50;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.partner-logo::before {
    content: '';
    position: absolute;
    bottom: -32px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 8px solid #2c3e50;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.partner-logo:hover::after,
.partner-logo:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Keyframe for infinite sliding */
@keyframes slide {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Fade effect on edges */
.partners-slider-container::before,
.partners-slider-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100px;
    z-index: 5;
    pointer-events: none;
}

.partners-slider-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.9), transparent);
}

.partners-slider-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.9), transparent);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .partner-logo {
        flex: 0 0 180px;
        margin: 0 25px;
    }
    
    .partner-logo img {
        max-width: 130px;
        max-height: 70px;
    }
}

@media (max-width: 768px) {
    .partners-section {
        padding: 60px 0;
    }
    
    .partners-section .section-header h2 {
        font-size: 2rem;
    }
    
    .partners-section .section-header {
        margin-bottom: 40px;
    }
    
    .partners-slider-container {
        padding: 30px 0;
        margin: 0 15px;
    }
    
    .partner-logo {
        flex: 0 0 150px;
        margin: 0 20px;
        height: 100px;
    }
    
    .partner-logo img {
        max-width: 110px;
        max-height: 60px;
    }
    
    .partners-slider-container::before,
    .partners-slider-container::after {
        width: 50px;
    }
}

@media (max-width: 480px) {
    .partners-section .section-header h2 {
        font-size: 1.75rem;
    }
    
    .partner-logo {
        flex: 0 0 120px;
        margin: 0 15px;
        height: 80px;
    }
    
    .partner-logo img {
        max-width: 90px;
        max-height: 50px;
    }
    
    .partner-logo::after {
        font-size: 0.75rem;
        padding: 6px 10px;
    }
}

/* Loading State */
.partners-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #6c757d;
    font-size: 1.1rem;
}

.partners-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Partners State */
.no-partners {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-partners i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-partners h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #495057;
}

/* Accessibility */
.partner-logo:focus {
    outline: 2px solid #3498db;
    outline-offset: 4px;
}

/* Print styles */
@media print {
    .partners-section {
        background: white !important;
        padding: 20px 0 !important;
    }
    
    .partners-slider-track {
        animation: none !important;
        flex-wrap: wrap;
        width: auto !important;
    }
    
    .partner-logo {
        flex: 0 0 auto;
        margin: 10px;
        page-break-inside: avoid;
    }
    
    .partner-logo img {
        filter: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .partners-section {
        background: white;
    }
    
    .partner-logo img {
        filter: none;
        border: 2px solid #000;
    }
    
    .partner-logo::after {
        background: #000;
        color: #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .partners-slider-track {
        animation: none;
    }
    
    .partner-logo {
        transition: none;
    }
    
    .partner-logo img {
        transition: none;
    }
}
