// Simplified Booking Form JavaScript - Similar to Contact Form
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const dateInput = document.getElementById('preferredDate');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.setAttribute('min', today);
    }

    // Form validation
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', function(e) {
            // Basic form validation
            let name = document.getElementById('name').value;
            let email = document.getElementById('email').value;
            let phone = document.getElementById('phone').value;
            let service = document.getElementById('service').value;
            let preferredDate = document.getElementById('preferredDate').value;
            let preferredTime = document.getElementById('preferredTime').value;
            let careType = document.getElementById('careType').value;
            let emergencyContact = document.getElementById('emergencyContact').value;
            let emergencyPhone = document.getElementById('emergencyPhone').value;
            let terms = document.getElementById('terms').checked;

            if (!name || !email || !phone || !service || !preferredDate || !preferredTime || !careType || !emergencyContact || !emergencyPhone) {
                alert('Please fill in all required fields.');
                e.preventDefault();
                return;
            }

            if (!terms) {
                alert('Please agree to the Terms and Conditions.');
                e.preventDefault();
                return;
            }

            // Email validation
            let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                e.preventDefault();
                return;
            }

            // Phone validation
            let phoneRegex = /^\+?[\d\s-]{10,}$/;
            if (!phoneRegex.test(phone)) {
                alert('Please enter a valid phone number.');
                e.preventDefault();
                return;
            }
            if (!phoneRegex.test(emergencyPhone)) {
                alert('Please enter a valid emergency contact phone number.');
                e.preventDefault();
                return;
            }

            // Date validation
            const selectedDate = new Date(preferredDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                alert('Please select a future date.');
                e.preventDefault();
                return;
            }

            // If validation passes, the form will submit to Formspree
        });
    }

    // Add visual feedback for form fields
    const formInputs = document.querySelectorAll('#bookingForm input, #bookingForm select, #bookingForm textarea');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = 'var(--primary-color)';
        });

        input.addEventListener('blur', function() {
            if (this.value) {
                this.style.borderColor = 'var(--success-color)';
            } else {
                this.style.borderColor = 'var(--gray-300)';
            }
        });
    });
});
