<?php
require_once 'includes/config.php';
require_once 'includes/Database.php';

// Get FAQs from database
$db = Database::getInstance();
$faqs = [];

try {
    // Get active FAQs ordered by display_order
    $faqs = $db->select('SELECT * FROM faqs WHERE status = "active" ORDER BY display_order ASC, id ASC');
} catch (Exception $e) {
    // Silently fail and use empty array
}

// Get categories for filter
$categories = [];
try {
    $categoryResults = $db->select('SELECT DISTINCT category FROM faqs WHERE status = "active" AND category IS NOT NULL ORDER BY category');
    foreach ($categoryResults as $cat) {
        if (!empty($cat['category'])) {
            $categories[] = $cat['category'];
        }
    }
} catch (Exception $e) {
    // Silently fail
}

// Get selected category from URL
$selectedCategory = isset($_GET['category']) ? $_GET['category'] : '';

include 'includes/header.php';
?>

<main class="container my-5 fixed-header-padding">
    <h1 class="mb-4 text-center">Frequently Asked Questions</h1>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <?php if (!empty($categories)): ?>
            <!-- Category Filter -->
            <div class="mb-4 text-center">
                <div class="category-filter">
                    <a href="faq.php" class="btn <?php echo empty($selectedCategory) ? 'btn-primary' : 'btn-outline-primary'; ?> mb-2">All</a>
                    <?php foreach ($categories as $category): ?>
                        <a href="faq.php?category=<?php echo urlencode($category); ?>" class="btn <?php echo $selectedCategory === $category ? 'btn-primary' : 'btn-outline-primary'; ?> mb-2">
                            <?php echo htmlspecialchars($category); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (empty($faqs)): ?>
                <div class="alert alert-info text-center">
                    <p>No FAQs found. Please check back later.</p>
                </div>
            <?php else: ?>
                <div class="accordion" id="faqAccordion">
                    <?php
                    $counter = 0;
                    foreach ($faqs as $index => $faq):
                        // Skip if category filter is active and doesn't match
                        if (!empty($selectedCategory) && $faq['category'] !== $selectedCategory) {
                            continue;
                        }
                        $counter++;
                        $isFirst = ($counter === 1);
                    ?>
                        <!-- FAQ Item <?php echo $counter; ?> -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading<?php echo $counter; ?>">
                                <button class="accordion-button <?php echo $isFirst ? '' : 'collapsed'; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $counter; ?>" aria-expanded="<?php echo $isFirst ? 'true' : 'false'; ?>" aria-controls="collapse<?php echo $counter; ?>">
                                    <?php echo htmlspecialchars($faq['question']); ?>
                                </button>
                            </h2>
                            <div id="collapse<?php echo $counter; ?>" class="accordion-collapse collapse <?php echo $isFirst ? 'show' : ''; ?>" aria-labelledby="heading<?php echo $counter; ?>" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <?php echo $faq['answer']; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if ($counter === 0): ?>
                    <div class="alert alert-info text-center">
                        <p>No FAQs found in this category. Please select another category or view all FAQs.</p>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="mt-5 text-center">
                <p>Don't see your question answered here? Contact us!</p>
                <a href="contact.php" class="btn btn-primary">Get in Touch</a>
            </div>
        </div>
    </div>
</main>

<?php include 'includes/footer.php'; ?>

<script>
    // Initialize Bootstrap accordion
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure Bootstrap is loaded
        if (typeof bootstrap !== 'undefined') {
            // Make sure the first item is shown by default
            var firstItem = document.getElementById('collapseOne');
            if (firstItem && !firstItem.classList.contains('show')) {
                var bsCollapse = new bootstrap.Collapse(firstItem, {
                    toggle: true
                });
            }

            // Fix any styling issues
            var accordionButtons = document.querySelectorAll('.accordion-button');
            accordionButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    // Force redraw to fix any visual glitches
                    setTimeout(function() {
                        window.dispatchEvent(new Event('resize'));
                    }, 300);
                });
            });
        } else {
            console.error('Bootstrap is not loaded. Please check your scripts.');
        }
    });
</script>
