<?php
require_once 'includes/auth.php';
require_once '../includes/Jobs.php';

$jobs = new Jobs();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $data = [
                    'title' => $_POST['title'],
                    'description' => $_POST['description'],
                    'requirements' => $_POST['requirements'],
                    'responsibilities' => $_POST['responsibilities'],
                    'location' => $_POST['location'],
                    'job_type' => $_POST['job_type'],
                    'experience_level' => $_POST['experience_level'],
                    'salary_range' => $_POST['salary_range'],
                    'application_deadline' => $_POST['application_deadline'] ?: null,
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];
                
                if ($jobs->createJob($data)) {
                    $message = "Job created successfully!";
                } else {
                    $error = "Failed to create job.";
                }
                break;
                
            case 'update':
                $data = [
                    'title' => $_POST['title'],
                    'description' => $_POST['description'],
                    'requirements' => $_POST['requirements'],
                    'responsibilities' => $_POST['responsibilities'],
                    'location' => $_POST['location'],
                    'job_type' => $_POST['job_type'],
                    'experience_level' => $_POST['experience_level'],
                    'salary_range' => $_POST['salary_range'],
                    'application_deadline' => $_POST['application_deadline'] ?: null,
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];
                
                if ($jobs->updateJob($_POST['job_id'], $data)) {
                    $message = "Job updated successfully!";
                } else {
                    $error = "Failed to update job.";
                }
                break;
                
            case 'delete':
                if ($jobs->deleteJob($_POST['job_id'])) {
                    $message = "Job deleted successfully!";
                } else {
                    $error = "Failed to delete job.";
                }
                break;
                
            case 'toggle_status':
                if ($jobs->toggleJobStatus($_POST['job_id'])) {
                    $message = "Job status updated successfully!";
                } else {
                    $error = "Failed to update job status.";
                }
                break;
        }
    }
}

// Get all jobs
$allJobs = $jobs->getAllJobs();
$jobStats = $jobs->getJobStats();

// Get job for editing if edit_id is provided
$editJob = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editJob = $jobs->getJobById($_GET['edit']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jobs Management - Admin Panel</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/sidebar.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .jobs-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .jobs-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .jobs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <h1>Jobs Management</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Jobs</h3>
                <div class="number"><?php echo $jobStats['total'] ?? 0; ?></div>
            </div>
            <div class="stat-card">
                <h3>Active Jobs</h3>
                <div class="number"><?php echo $jobStats['active'] ?? 0; ?></div>
            </div>
            <div class="stat-card">
                <h3>Applications</h3>
                <div class="number"><?php echo $jobs->getApplicationStats()['total'] ?? 0; ?></div>
            </div>
        </div>
        
        <!-- Job Form -->
        <div class="form-container">
            <h2><?php echo $editJob ? 'Edit Job' : 'Add New Job'; ?></h2>
            <form method="POST">
                <input type="hidden" name="action" value="<?php echo $editJob ? 'update' : 'create'; ?>">
                <?php if ($editJob): ?>
                    <input type="hidden" name="job_id" value="<?php echo $editJob['id']; ?>">
                <?php endif; ?>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="title">Job Title *</label>
                        <input type="text" id="title" name="title" required 
                               value="<?php echo $editJob ? htmlspecialchars($editJob['title']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" 
                               value="<?php echo $editJob ? htmlspecialchars($editJob['location']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="job_type">Job Type</label>
                        <select id="job_type" name="job_type">
                            <option value="full_time" <?php echo ($editJob && $editJob['job_type'] == 'full_time') ? 'selected' : ''; ?>>Full Time</option>
                            <option value="part_time" <?php echo ($editJob && $editJob['job_type'] == 'part_time') ? 'selected' : ''; ?>>Part Time</option>
                            <option value="contract" <?php echo ($editJob && $editJob['job_type'] == 'contract') ? 'selected' : ''; ?>>Contract</option>
                            <option value="internship" <?php echo ($editJob && $editJob['job_type'] == 'internship') ? 'selected' : ''; ?>>Internship</option>
                            <option value="trainee" <?php echo ($editJob && $editJob['job_type'] == 'trainee') ? 'selected' : ''; ?>>Trainee</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="experience_level">Experience Level</label>
                        <select id="experience_level" name="experience_level">
                            <option value="entry" <?php echo ($editJob && $editJob['experience_level'] == 'entry') ? 'selected' : ''; ?>>Entry Level</option>
                            <option value="mid" <?php echo ($editJob && $editJob['experience_level'] == 'mid') ? 'selected' : ''; ?>>Mid Level</option>
                            <option value="senior" <?php echo ($editJob && $editJob['experience_level'] == 'senior') ? 'selected' : ''; ?>>Senior Level</option>
                            <option value="executive" <?php echo ($editJob && $editJob['experience_level'] == 'executive') ? 'selected' : ''; ?>>Executive</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="salary_range">Salary Range</label>
                        <input type="text" id="salary_range" name="salary_range" 
                               placeholder="e.g., Rs. 40,000 - 60,000"
                               value="<?php echo $editJob ? htmlspecialchars($editJob['salary_range']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="application_deadline">Application Deadline</label>
                        <input type="date" id="application_deadline" name="application_deadline" 
                               value="<?php echo $editJob ? $editJob['application_deadline'] : ''; ?>">
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="description">Job Description *</label>
                        <textarea id="description" name="description" required><?php echo $editJob ? htmlspecialchars($editJob['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="requirements">Requirements</label>
                        <textarea id="requirements" name="requirements"><?php echo $editJob ? htmlspecialchars($editJob['requirements']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="responsibilities">Responsibilities</label>
                        <textarea id="responsibilities" name="responsibilities"><?php echo $editJob ? htmlspecialchars($editJob['responsibilities']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_active" name="is_active" 
                                   <?php echo (!$editJob || $editJob['is_active']) ? 'checked' : ''; ?>>
                            <label for="is_active">Active</label>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <?php echo $editJob ? 'Update Job' : 'Create Job'; ?>
                </button>
                
                <?php if ($editJob): ?>
                    <a href="jobs.php" class="btn btn-secondary">Cancel</a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Jobs List -->
        <div class="jobs-table">
            <table>
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Location</th>
                        <th>Type</th>
                        <th>Experience</th>
                        <th>Status</th>
                        <th>Applications</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($allJobs as $job): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($job['title']); ?></strong>
                                <?php if ($job['salary_range']): ?>
                                    <br><small><?php echo htmlspecialchars($job['salary_range']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($job['location'] ?: 'Not specified'); ?></td>
                            <td><?php echo ucfirst(str_replace('_', ' ', $job['job_type'])); ?></td>
                            <td><?php echo ucfirst($job['experience_level']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $job['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $job['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $applicationCount = count($jobs->getJobApplications($job['id']));
                                echo $applicationCount;
                                ?>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($job['created_at'])); ?></td>
                            <td>
                                <div class="actions">
                                    <a href="jobs.php?edit=<?php echo $job['id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>

                                    <form method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to toggle this job status?');">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="job_id" value="<?php echo $job['id']; ?>">
                                        <button type="submit" class="btn <?php echo $job['is_active'] ? 'btn-warning' : 'btn-success'; ?> btn-sm">
                                            <i class="fas <?php echo $job['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                        </button>
                                    </form>

                                    <form method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to delete this job? This action cannot be undone.');">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="job_id" value="<?php echo $job['id']; ?>">
                                        <button type="submit" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>

                    <?php if (empty($allJobs)): ?>
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px;">
                                <i class="fas fa-briefcase" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>
                                <p>No jobs found. Create your first job posting!</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Auto-resize textareas
        document.querySelectorAll('textarea').forEach(textarea => {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        });
    </script>
</body>
</html>
