# Partners Management System

## Overview
The Partners Management System allows you to showcase partner/collaborator logos on your website with a modern, infinite scrolling slider. Partners can be easily managed through the admin panel.

## Features
- **Dynamic Logo Slider**: Modern, smooth infinite scrolling animation
- **Admin Management**: Full CRUD operations for partners
- **Responsive Design**: Works perfectly on all devices
- **Hover Effects**: Interactive tooltips and animations
- **Status Management**: Enable/disable partners easily
- **Display Order**: Control the order partners appear
- **File Upload**: Secure logo upload with validation

## Admin Panel Access
Navigate to: `admin/partners.php`

## Partner Management

### Adding a New Partner
1. Click "Add New Partner" button
2. Fill in the partner details:
   - **Name**: Partner/company name
   - **Logo**: Upload image file (JPG, PNG, GIF, WebP - max 5MB)
   - **Website URL**: Optional partner website
   - **Description**: Optional description
   - **Display Order**: Lower numbers appear first

### Editing a Partner
1. Click the edit (pencil) icon next to any partner
2. Modify the details as needed
3. Upload a new logo if desired (leave empty to keep current)
4. Save changes

### Managing Partner Status
- Click the play/pause icon to toggle active/inactive status
- Only active partners appear on the website
- Inactive partners are hidden but not deleted

### Deleting Partners
- Click the trash icon to permanently delete a partner
- **Warning**: This action cannot be undone
- The logo file will also be removed from the server

## Technical Details

### Database Table: `partners`
```sql
CREATE TABLE partners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    logo VARCHAR(255) NOT NULL,
    website_url VARCHAR(255) DEFAULT NULL,
    description TEXT DEFAULT NULL,
    display_order INT DEFAULT 0,
    status ENUM('active','inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Files Structure
```
includes/Partners.php          # Partner management class
admin/partners.php            # Admin interface
css/partners.css              # Styling for partners section
uploads/partners/             # Logo storage directory
sql/create_partners_table.sql # Database setup
```

### Logo Requirements
- **Formats**: JPG, JPEG, PNG, GIF, WebP
- **Max Size**: 5MB
- **Recommended Dimensions**: 200x100px (2:1 ratio)
- **Background**: Transparent or white recommended

## Customization

### Animation Speed
The slider animation duration automatically adjusts based on the number of partners:
- Base duration: 20 seconds
- Additional time: 2.5 seconds per partner

### Styling
Modify `css/partners.css` to customize:
- Colors and gradients
- Animation speed
- Hover effects
- Responsive breakpoints

### Logo Display
- Logos are displayed in grayscale by default
- Color appears on hover
- Clicking opens partner website (if provided)
- Tooltips show partner names

## Security Features
- File type validation
- File size limits
- Secure file naming (timestamped)
- Directory traversal protection
- SQL injection prevention

## Troubleshooting

### Partners not showing
1. Check partner status is "active"
2. Verify logo file exists in `uploads/partners/`
3. Check file permissions on uploads directory

### Logo upload failing
1. Ensure file size is under 5MB
2. Check file format is supported
3. Verify uploads directory is writable (755 permissions)

### Animation not smooth
1. Check number of partners (too few may cause gaps)
2. Verify all logo files load correctly
3. Check browser console for JavaScript errors

## Best Practices
1. **Logo Quality**: Use high-quality, consistent logos
2. **File Naming**: Use descriptive names for easy identification
3. **Regular Cleanup**: Remove unused logo files periodically
4. **Backup**: Regular backup of uploads directory
5. **Testing**: Test on different devices and browsers

## Support
For technical support or feature requests, contact the development team.
