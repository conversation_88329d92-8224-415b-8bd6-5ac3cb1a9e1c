</main>
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <!-- Quick Links -->
                <div class="footer-section" data-aos="fade-up">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                        <li><a href="blog.php">Blog</a></li>
                        <li><a href="faq.php">FAQs</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div class="footer-section" data-aos="fade-up" data-aos-delay="100">
                    <h3>Our Services</h3>
                    <ul>
                        <li><a href="services.php#home-nursing">Home Medical Care</a></li>
                        <li><a href="services.php#elderly-assistance">Elderly Assistance</a></li>
                        <li><a href="services.php#physical-therapy">Physical Therapy</a></li>
                        <li><a href="services.php#medical-support">24/7 Doctor Support</a></li>
                        <li><a href="services.php#companionship">Companionship Care</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div class="footer-section" data-aos="fade-up" data-aos-delay="200">
                    <h3>Contact Us</h3>
                    <ul class="contact-info">
                        <li><i class="fas fa-phone"></i> <?php echo htmlspecialchars($settingsHandler->get('contact_phone', '+977 986-0102404')); ?></li>
                        <li><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($settingsHandler->get('contact_email', '<EMAIL>')); ?></li>
                        <li><i class="fas fa-location-dot"></i> <?php echo htmlspecialchars($settingsHandler->get('address', 'khursanitar marg, Kathmandu, Nepal')); ?></li>
                    </ul>

                    <!-- Social Media Icons -->
                    <div class="social-icons" style="margin-top: 20px;">
                        <h4 style="margin-bottom: 15px; font-size: 16px; color: #fff;">Follow Us</h4>
                        <?php
                        // Get social media links from settings
                        $facebook = $settingsHandler->get('facebook', '');
                        $tiktok = $settingsHandler->get('tiktok', '');
                        $instagram = $settingsHandler->get('instagram', '');
                        $linkedin = $settingsHandler->get('linkedin', '');
                        $youtube = $settingsHandler->get('youtube', '');

                        if (!empty($facebook)):
                        ?>
                        <a href="<?php echo htmlspecialchars($facebook); ?>" class="social-icon facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <?php endif;
                        if (!empty($tiktok)):
                        ?>
                        <a href="<?php echo htmlspecialchars($tiktok); ?>" class="social-icon tiktok" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M9 0h1.98c.144.715.54 1.617 1.235 2.512C12.895 3.389 13.797 4 15 4v2c-1.753 0-3.07-.814-4-1.829V11a5 5 0 1 1-5-5v2a3 3 0 1 0 3 3V0Z"/>
                            </svg>
                        </a>
                        <?php endif;
                        if (!empty($instagram)):
                        ?>
                        <a href="<?php echo htmlspecialchars($instagram); ?>" class="social-icon instagram" target="_blank"><i class="fab fa-instagram"></i></a>
                        <?php endif;
                        if (!empty($linkedin)):
                        ?>
                        <a href="<?php echo htmlspecialchars($linkedin); ?>" class="social-icon linkedin" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                        <?php endif;
                        if (!empty($youtube)):
                        ?>
                        <a href="<?php echo htmlspecialchars($youtube); ?>" class="social-icon youtube" target="_blank"><i class="fab fa-youtube"></i></a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($settingsHandler->get('site_name', 'Doctors At Door Step')); ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>