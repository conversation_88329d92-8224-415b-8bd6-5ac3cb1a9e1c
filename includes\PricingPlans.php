<?php
require_once 'Database.php';
require_once 'Settings.php';

class PricingPlans {
    private $db;
    private $settings;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->settings = new Settings();
    }

    /**
     * Get all active pricing plans
     * @return array Pricing plans data
     */
    public function getAllActivePlans() {
        try {
            return $this->db->select(
                "SELECT * FROM pricing_plans WHERE is_active = 1 ORDER BY display_order ASC"
            );
        } catch (Exception $e) {
            error_log("Error getting pricing plans: " . $e->getMessage(), 3, "../logs/error.log");

            // Return default plans if database query fails
            return $this->getDefaultPlans();
        }
    }

    /**
     * Get a specific pricing plan by ID
     * @param int $id Plan ID
     * @return array|null Plan data or null if not found
     */
    public function getPlanById($id) {
        try {
            return $this->db->selectOne(
                "SELECT * FROM pricing_plans WHERE id = ? AND is_active = 1",
                [$id]
            );
        } catch (Exception $e) {
            error_log("Error getting pricing plan by ID: " . $e->getMessage(), 3, "../logs/error.log");
            return null;
        }
    }

    /**
     * Get a specific pricing plan by name
     * @param string $name Plan name (e.g., 'basic', 'standard', 'premium')
     * @return array|null Plan data or null if not found
     */
    public function getPlanByName($name) {
        try {
            return $this->db->selectOne(
                "SELECT * FROM pricing_plans WHERE name = ? AND is_active = 1",
                [$name]
            );
        } catch (Exception $e) {
            error_log("Error getting pricing plan by name: " . $e->getMessage(), 3, "../logs/error.log");
            return null;
        }
    }

    /**
     * Format price with currency symbol
     * @param float $price Price value
     * @return string Formatted price with currency symbol
     */
    public function formatPrice($price) {
        $currencySymbol = $this->settings->get('currency_symbol', 'Rs.');
        return $currencySymbol . ' ' . number_format($price, 0);
    }

    /**
     * Get features as an array
     * @param string $featuresString Features as a newline-separated string
     * @return array Features as an array
     */
    public function getFeaturesArray($featuresString) {
        return explode("\n", $featuresString);
    }

    /**
     * Get default pricing plans if database query fails
     * @return array Default pricing plans
     */
    private function getDefaultPlans() {
        return [
            [
                'id' => 1,
                'name' => 'basic',
                'title' => 'Essential Care',
                'price' => 4900.00,
                'period' => '/hour',
                'description' => 'Perfect for basic assistance and companionship needs',
                'features' => "Basic health monitoring\nMedication reminders\nLight housekeeping\nCompanionship services\nBasic personal care",
                'is_recommended' => 0,
                'display_order' => 1,
                'is_active' => 1
            ],
            [
                'id' => 2,
                'name' => 'standard',
                'title' => 'Personalized Care',
                'price' => 7900.00,
                'period' => '/hour',
                'description' => 'Comprehensive care with personalized attention',
                'features' => "All Basic features, plus:\nSkilled nursing care\nPhysical therapy sessions\nMedication management\nPersonal care assistance\nTransportation services",
                'is_recommended' => 1,
                'display_order' => 2,
                'is_active' => 1
            ],
            [
                'id' => 3,
                'name' => 'premium',
                'title' => '24/7 Assistance',
                'price' => 10900.00,
                'period' => '/hour',
                'description' => 'Complete care with round-the-clock support',
                'features' => "All Standard features, plus:\n24/7 dedicated care\nAdvanced medical monitoring\nEmergency response system\nCare coordination\nSpecialized therapy services\nFamily care consulting",
                'is_recommended' => 0,
                'display_order' => 3,
                'is_active' => 1
            ]
        ];
    }
}
