<?php
require_once 'includes/Services.php';

// Get service ID from URL
$serviceId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Initialize Services handler
$servicesHandler = new Services();

// Get service details
$serviceData = $servicesHandler->getServiceById($serviceId);

// Redirect to services page if service not found
if (!$serviceData) {
    header('Location: services.php');
    exit();
}

// Parse features and benefits from description
$description_parts = explode("\n", $serviceData['description']);
$main_description = array_shift($description_parts);

// Default features and benefits if not specified in description
$features = [
    'Professional medical care by qualified doctors',
    '24/7 availability for medical needs',
    'Personalized care plans',
    'Regular health monitoring',
    'Emergency response services',
    'Comprehensive medical support'
];

$benefits = [
    'Care in comfortable home environment',
    'Reduced hospital visits',
    'Better recovery outcomes',
    'Peace of mind for family',
    'Cost-effective healthcare solution'
];

// Add features and benefits to serviceData if they don't exist
if (!isset($serviceData['features'])) {
    $serviceData['features'] = $features;
}

if (!isset($serviceData['benefits'])) {
    $serviceData['benefits'] = $benefits;
}
?>

<?php include 'includes/header.php'; ?>

<!-- Service Hero -->
<section class="page-hero service-hero" data-aos="fade-up" style="background-image: linear-gradient(rgba(26, 43, 60, 0.8), rgba(26, 43, 60, 0.8)), url('images/hero-banner.jpg">
    <div class="container">
        <h1><?php echo $serviceData['title']; ?></h1>
        <p><?php echo $serviceData['description']; ?></p>
    </div>
</section>

<!-- Service Details -->
<section class="service-details-section" data-aos="fade-up">
    <div class="container">
        <div class="service-details-grid">
            <!-- Main Content -->
            <div class="service-main-content">
                <h2>Service Features</h2>
                <ul class="service-features">
                    <?php foreach ($serviceData['features'] as $feature): ?>
                        <li data-aos="fade-up">
                            <i class="fas fa-check-circle"></i>
                            <span><?php echo $feature; ?></span>
                        </li>
                    <?php endforeach; ?>
                </ul>

                <h2>Key Benefits</h2>
                <ul class="service-benefits">
                    <?php foreach ($serviceData['benefits'] as $benefit): ?>
                        <li data-aos="fade-up">
                            <i class="fas fa-star"></i>
                            <span><?php echo $benefit; ?></span>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Sidebar -->
            <div class="service-sidebar">
                <!--<div class="pricing-box" data-aos="fade-left">
                    <h3>Service Details</h3>
                        <div class="price-item">
                            <span class="label">Duration:</span>
                            <span class="value"><?php echo $servicesHandler->formatDuration($serviceData['duration']); ?></span>
                        </div>
                    </div>                        -->
                    <p class="pricing-note">* Final price may vary based on specific care requirements and duration and will be discussed on call.</p>
                    <a href="booking.php?id=<?php echo $serviceData['id']; ?>" class="btn btn-primary">Book This Service</a>
                </div>

                <div class="contact-box" data-aos="fade-left">
                    <h3>Need More Information?</h3>
                    <p>Speak with our care coordinator to learn more about this service.</p>
                    <a href="tel:+97701456789" class="phone-link">
                        <i class="fas fa-phone"></i>
                        +977 986-0102404
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php
// Get testimonials data with fixed image paths
require_once 'includes/Testimonials.php';
$testimonialHandler = new Testimonials();
$testimonials = $testimonialHandler->getAllActiveTestimonialsForDisplay(); // Full content display
?>
<!-- Service Testimonials -->
<section class="testimonials">
    <div class="container">
        <div class="section-header">
            <h2>What Our Clients Say</h2>
            <p>Real experiences from families we've helped</p>
        </div>
        
        <div class="testimonials-slider">
            <!-- Swiper Container -->
            <div class="swiper-container testimonials-swiper">
                <div class="swiper-wrapper">
                    <?php if (empty($testimonials)): ?>
                        <!-- Fallback testimonials with full content -->
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"The care and attention provided by the doctors was exceptional. They made my recovery process so much more comfortable in my own home. The professional approach and genuine concern for my wellbeing really stood out."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Raj Thapa</h4>
                                        <p>Patient</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"Professional medical services at home made all the difference for our elderly father. The doctors were punctual, caring, and highly skilled. We couldn't have asked for better healthcare support."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Sita Sharma</h4>
                                        <p>Family Member</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"Excellent doorstep medical care with qualified doctors. The convenience and quality exceeded our expectations completely."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Ram Bahadur</h4>
                                        <p>Senior Citizen</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($testimonials as $testimonial): ?>
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-content">
                                        <p>"<?php echo htmlspecialchars($testimonial['display_content']); ?>"</p>
                                    </div>
                                    <div class="testimonial-author">
                                        <!-- Enhanced image path handling for webserver compatibility -->
                                        <?php if (!empty($testimonial['photo_path'])): ?>
                                            <div class="testimonial-author-image" style="background-image: url('<?php echo htmlspecialchars($testimonial['photo_path']); ?>'); background-size: cover; background-position: center;"></div>
                                        <?php else: ?>
                                            <div class="testimonial-author-image no-image">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="author-info">
                                            <h4><?php echo htmlspecialchars($testimonial['name']); ?></h4>
                                            <p><?php echo htmlspecialchars($testimonial['position']); ?></p>
                                            <?php if ($testimonial['rating'] > 0): ?>
                                            <div class="testimonial-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $testimonial['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
            </div>
            
            <!-- Add Navigation -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </div>
</section>
<!-- FAQ Section -->
<section class="service-faq" data-aos="fade-up">
    <div class="container">
        <h2>Frequently Asked Questions</h2>
        <div class="faq-accordion">
            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>What qualifications do your doctors have?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>All our doctors are licensed medical professionals with extensive experience in their respective fields. They undergo regular training and certification updates to maintain the highest standards of medical care.</p>
                </div>
            </div>

            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>How quickly can you start providing care?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>We can typically begin services within 24-48 hours of initial contact, depending on your specific needs and care requirements. Emergency services can be arranged more quickly when needed.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Booking CTA -->
<section class="booking-cta" data-aos="fade-up">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Start Your Care Journey?</h2>
            <p>Book a consultation today to discuss your specific care needs and create a personalized care plan.</p>
            <div class="cta-buttons">
                <a href="booking.php?id=<?php echo $serviceData['id']; ?>" class="btn btn-primary">Book This Service</a>
                <a href="contact.php" class="btn btn-outline">Contact Us</a>
            </div>
        </div>
    </div>
</section>
<!-- Testimonials Slider Initialization Script -->
<script>
// Testimonials Slider Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize testimonials slider
    if (document.querySelector('.testimonials-swiper')) {
        // Check if Swiper is already loaded
        if (typeof Swiper !== 'undefined') {
            initTestimonialsSlider();
        } else {
            // If Swiper isn't loaded yet, wait for a moment and try again
            setTimeout(function() {
                if (typeof Swiper !== 'undefined') {
                    initTestimonialsSlider();
                } else {
                    console.error('Swiper library not loaded properly');
                }
            }, 1000);
        }
    }
});

function initTestimonialsSlider() {
    // Simple configuration without complex animations
    var testimonialSwiper = new Swiper('.testimonials-swiper', {
        // Optional parameters
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        // Responsive breakpoints
        breakpoints: {
            // when window width is >= 768px
            768: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            // when window width is >= 1024px
            1024: {
                slidesPerView: 2,
                spaceBetween: 30
            }
        },
        // Navigation arrows
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        // Pagination
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
}
</script>
<?php include 'includes/footer.php'; ?>
