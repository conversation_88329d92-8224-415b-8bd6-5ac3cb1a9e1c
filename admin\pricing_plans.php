<?php
session_start();
require_once 'includes/auth.php';
require_admin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Include Database and Settings classes
require_once '../includes/Database.php';
$db = Database::getInstance();

// Message handling
$message = '';
$messageType = '';

// Handle form submission for adding/editing pricing plan
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add' || $action === 'edit') {
        // Get form data
        $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
        $name = trim($_POST['name']);
        $title = trim($_POST['title']);
        $price = (float)$_POST['price'];
        $period = trim($_POST['period']);
        $description = trim($_POST['description']);
        $features = trim($_POST['features']);
        $isRecommended = isset($_POST['is_recommended']) ? 1 : 0;
        $displayOrder = (int)$_POST['display_order'];
        $isActive = isset($_POST['is_active']) ? 1 : 0;

        // Validate required fields
        if (empty($name) || empty($title) || $price <= 0) {
            $message = 'Please fill in all required fields.';
            $messageType = 'error';
        } else {
            try {
                if ($action === 'add') {
                    // Insert new pricing plan
                    $db->insert('pricing_plans', [
                        'name' => $name,
                        'title' => $title,
                        'price' => $price,
                        'period' => $period,
                        'description' => $description,
                        'features' => $features,
                        'is_recommended' => $isRecommended,
                        'display_order' => $displayOrder,
                        'is_active' => $isActive
                    ]);

                    $message = 'Pricing plan added successfully!';
                    $messageType = 'success';
                } else {
                    // Update existing pricing plan
                    try {
                        $db->update(
                            'pricing_plans',
                            [
                                'name' => $name,
                                'title' => $title,
                                'price' => $price,
                                'period' => $period,
                                'description' => $description,
                                'features' => $features,
                                'is_recommended' => $isRecommended,
                                'display_order' => $displayOrder,
                                'is_active' => $isActive
                            ],
                            'id = ?',
                            [$id]
                        );

                        $message = 'Pricing plan updated successfully!';
                        $messageType = 'success';
                    } catch (Exception $updateError) {
                        error_log("Error updating pricing plan: " . $updateError->getMessage());

                        // Try direct query as fallback
                        try {
                            $conn = $db->getConnection();
                            $stmt = $conn->prepare("UPDATE pricing_plans SET
                                name = ?,
                                title = ?,
                                price = ?,
                                period = ?,
                                description = ?,
                                features = ?,
                                is_recommended = ?,
                                display_order = ?,
                                is_active = ?
                                WHERE id = ?");

                            $stmt->execute([
                                $name,
                                $title,
                                $price,
                                $period,
                                $description,
                                $features,
                                $isRecommended,
                                $displayOrder,
                                $isActive,
                                $id
                            ]);

                            $message = 'Pricing plan updated successfully!';
                            $messageType = 'success';
                        } catch (Exception $directError) {
                            $message = 'Error updating pricing plan: ' . $directError->getMessage();
                            $messageType = 'error';
                        }
                    }
                }
            } catch (Exception $e) {
                $message = 'Error: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    } elseif ($action === 'delete' && isset($_POST['id'])) {
        // Delete pricing plan
        $id = (int)$_POST['id'];

        try {
            $db->delete('pricing_plans', 'id = ?', [$id]);
            $message = 'Pricing plan deleted successfully!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get pricing plan for editing if ID is provided
$editPlan = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $editPlan = $db->selectOne('SELECT * FROM pricing_plans WHERE id = ?', [$editId]);
}

// Get all pricing plans
$pricingPlans = $db->select('SELECT * FROM pricing_plans ORDER BY display_order ASC');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Pricing Plans - Doctors At Door Step</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Same CSS as in settings.php */
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--dark-color);
            color: var(--white);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 20px;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: rgba(44, 123, 229, 0.2);
            color: var(--primary-color);
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: absolute;
            bottom: 0;
            width: 100%;
        }

        .logout-btn {
            display: block;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        /* Alert Messages */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-group .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-cancel {
            padding: 10px 20px;
            background-color: var(--light-color);
            color: var(--dark-color);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-cancel:hover {
            background-color: #e0e0e0;
        }

        .btn-submit {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-submit:hover {
            background-color: var(--dark-color);
        }

        .btn-danger {
            background-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #dc2626;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        th {
            background-color: var(--light-color);
            font-weight: 600;
        }

        tr:hover {
            background-color: rgba(44, 123, 229, 0.05);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-icon {
            padding: 5px 10px;
            border-radius: 5px;
            color: var(--white);
            text-decoration: none;
            font-size: 14px;
        }

        .btn-edit {
            background-color: var(--primary-color);
        }

        .btn-delete {
            background-color: var(--error-color);
        }

        /* Responsive */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .toggle-sidebar {
                display: block;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">Manage Pricing Plans</h1>
            <?php if (!$editPlan): ?>
                <a href="?add=true" class="btn-submit">Add New Plan</a>
            <?php endif; ?>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($editPlan || isset($_GET['add'])): ?>
            <!-- Add/Edit Form -->
            <div class="form-container">
                <h2><?php echo $editPlan ? 'Edit Pricing Plan' : 'Add New Pricing Plan'; ?></h2>
                <form method="POST" action="pricing_plans.php">
                    <input type="hidden" name="action" value="<?php echo $editPlan ? 'edit' : 'add'; ?>">
                    <?php if ($editPlan): ?>
                        <input type="hidden" name="id" value="<?php echo $editPlan['id']; ?>">
                    <?php endif; ?>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Plan Name *</label>
                            <input type="text" id="name" name="name" value="<?php echo $editPlan ? htmlspecialchars($editPlan['name']) : ''; ?>" required>
                            <small class="text-muted">Lowercase, no spaces (e.g., basic, standard, premium)</small>
                        </div>

                        <div class="form-group">
                            <label for="title">Plan Title *</label>
                            <input type="text" id="title" name="title" value="<?php echo $editPlan ? htmlspecialchars($editPlan['title']) : ''; ?>" required>
                            <small class="text-muted">Display title (e.g., Essential Care)</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="price">Price *</label>
                            <input type="number" id="price" name="price" step="0.01" value="<?php echo $editPlan ? htmlspecialchars($editPlan['price']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="period">Period</label>
                            <input type="text" id="period" name="period" value="<?php echo $editPlan ? htmlspecialchars($editPlan['period']) : '/hour'; ?>">
                            <small class="text-muted">e.g., /hour, /month, /visit</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <input type="text" id="description" name="description" value="<?php echo $editPlan ? htmlspecialchars($editPlan['description']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="features">Features</label>
                        <textarea id="features" name="features" rows="6"><?php echo $editPlan ? htmlspecialchars($editPlan['features']) : ''; ?></textarea>
                        <small class="text-muted">Enter one feature per line</small>
                    </div>

                    <div class="form-group">
                        <label for="display_order">Display Order</label>
                        <input type="number" id="display_order" name="display_order" value="<?php echo $editPlan ? htmlspecialchars($editPlan['display_order']) : '0'; ?>">
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_recommended" name="is_recommended" value="1" <?php echo ($editPlan && $editPlan['is_recommended'] == 1) ? 'checked' : ''; ?>>
                            <label for="is_recommended">Recommended Plan</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_active" name="is_active" value="1" <?php echo (!$editPlan || $editPlan['is_active'] == 1) ? 'checked' : ''; ?>>
                            <label for="is_active">Active</label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a href="pricing_plans.php" class="btn-cancel">Cancel</a>
                        <button type="submit" class="btn-submit">Save Plan</button>
                    </div>
                </form>
            </div>
        <?php else: ?>
            <!-- Pricing Plans Table -->
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Title</th>
                            <th>Price</th>
                            <th>Recommended</th>
                            <th>Active</th>
                            <th>Order</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($pricingPlans)): ?>
                            <tr>
                                <td colspan="7">No pricing plans found. <a href="?add=true">Add one</a> or <a href="init_pricing_plans.php">initialize default plans</a>.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($pricingPlans as $plan): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($plan['name']); ?></td>
                                    <td><?php echo htmlspecialchars($plan['title']); ?></td>
                                    <td><?php echo htmlspecialchars($plan['price']) . htmlspecialchars($plan['period']); ?></td>
                                    <td><?php echo $plan['is_recommended'] ? 'Yes' : 'No'; ?></td>
                                    <td><?php echo $plan['is_active'] ? 'Yes' : 'No'; ?></td>
                                    <td><?php echo htmlspecialchars($plan['display_order']); ?></td>
                                    <td class="action-buttons">
                                        <a href="?edit=<?php echo $plan['id']; ?>" class="btn-icon btn-edit"><i class="fas fa-edit"></i></a>
                                        <form method="POST" action="pricing_plans.php" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this plan?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $plan['id']; ?>">
                                            <button type="submit" class="btn-icon btn-delete"><i class="fas fa-trash"></i></button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
