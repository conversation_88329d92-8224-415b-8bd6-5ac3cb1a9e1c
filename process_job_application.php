<?php
require_once 'includes/config.php';
require_once 'includes/Jobs.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Initialize response
$response = [
    'success' => false,
    'message' => 'An error occurred while processing your application.'
];

// Function to validate reCAPTCHA
function verifyRecaptcha($recaptcha_response) {
    $secret_key = "6LeMkPwqAAAAAGoWHcfuDB-Rq1BBUZa5xD7dhKVr";
    $url = "https://www.google.com/recaptcha/api/siteverify";
    $data = [
        'secret' => $secret_key,
        'response' => $recaptcha_response
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    $result = json_decode($response);

    return $result->success;
}

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to handle file upload
function handleResumeUpload($file) {
    $upload_dir = 'uploads/resumes/';
    
    // Create directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Validate file
    $allowed_types = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    $max_size = 2 * 1024 * 1024; // 2MB
    
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'message' => 'Invalid file type. Please upload PDF, DOC, or DOCX files only.'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'File size too large. Maximum size is 2MB.'];
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_filename = uniqid('resume_') . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $unique_filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return [
            'success' => true,
            'filename' => $file['name'],
            'path' => $upload_path
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file.'];
    }
}

// Function to send notification via Formspree
function send_formspree_notification($data) {
    $formspree_url = 'https://formspree.io/f/xqabgezq';
    
    try {
        // Prepare the data for Formspree
        $formspree_data = [
            'name' => $data['applicant_name'],
            'email' => $data['applicant_email'],
            'phone' => $data['applicant_phone'],
            'subject' => 'New Job Application - ' . $data['job_title'],
            'message' => "📋 NEW JOB APPLICATION\n\n" .
                        "Position: " . $data['job_title'] . "\n" .
                        "Applicant: " . $data['applicant_name'] . "\n" .
                        "Email: " . $data['applicant_email'] . "\n" .
                        "Phone: " . $data['applicant_phone'] . "\n\n" .
                        "Cover Letter:\n" . $data['cover_letter'] . "\n\n" .
                        "Resume: " . $data['resume_filename'] . "\n" .
                        "Application submitted at: " . date('Y-m-d H:i:s')
        ];

        // Initialize cURL
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $formspree_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($formspree_data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'User-Agent: ' . SITE_NAME . ' Job Application System'
            ]
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        // Check for cURL errors
        if ($curl_error) {
            error_log("Formspree cURL Error: " . $curl_error);
            return false;
        }
        
        // Check HTTP response code
        if ($http_code >= 200 && $http_code < 300) {
            return true;
        } else {
            error_log("Formspree HTTP Error: " . $http_code . " - " . $response);
            return false;
        }
        
    } catch (Exception $e) {
        error_log("Formspree Exception: " . $e->getMessage());
        return false;
    }
}

// Only process POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit;
}

try {
    // Verify reCAPTCHA
    if (isset($_POST['g-recaptcha-response'])) {
        $recaptcha_response = $_POST['g-recaptcha-response'];
        if (!verifyRecaptcha($recaptcha_response)) {
            $response['message'] = "reCAPTCHA verification failed. Please try again.";
            echo json_encode($response);
            exit;
        }
    } else {
        $response['message'] = "Please complete the reCAPTCHA verification.";
        echo json_encode($response);
        exit;
    }
    
    // Validate required fields
    $required_fields = ['applicant_name', 'applicant_email', 'applicant_phone', 'cover_letter', 'job_title'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $response['message'] = "Please fill in all required fields.";
            echo json_encode($response);
            exit;
        }
    }
    
    // Validate email
    if (!filter_var($_POST['applicant_email'], FILTER_VALIDATE_EMAIL)) {
        $response['message'] = "Please enter a valid email address.";
        echo json_encode($response);
        exit;
    }
    
    // Validate file upload
    if (!isset($_FILES['resume']) || $_FILES['resume']['error'] !== UPLOAD_ERR_OK) {
        $response['message'] = "Please upload your resume.";
        echo json_encode($response);
        exit;
    }
    
    // Handle file upload
    $upload_result = handleResumeUpload($_FILES['resume']);
    if (!$upload_result['success']) {
        $response['message'] = $upload_result['message'];
        echo json_encode($response);
        exit;
    }
    
    // Sanitize input data
    $application_data = [
        'job_id' => isset($_POST['job_id']) ? (int)$_POST['job_id'] : null,
        'applicant_name' => sanitize_input($_POST['applicant_name']),
        'applicant_email' => sanitize_input($_POST['applicant_email']),
        'applicant_phone' => sanitize_input($_POST['applicant_phone']),
        'cover_letter' => sanitize_input($_POST['cover_letter']),
        'job_title' => sanitize_input($_POST['job_title']),
        'resume_filename' => $upload_result['filename'],
        'resume_path' => $upload_result['path']
    ];
    
    // Save to database
    $jobs = new Jobs();
    $application_id = $jobs->submitApplication($application_data);
    
    if ($application_id) {
        // Send notification via Formspree
        $formspree_sent = send_formspree_notification($application_data);
        
        $response['success'] = true;
        $response['message'] = "Your application has been submitted successfully! We will review it and contact you if your qualifications match our requirements.";
        $response['application_id'] = $application_id;
        
        if (DEBUG_MODE) {
            $response['debug_info'] = [
                'formspree_sent' => $formspree_sent,
                'resume_path' => $upload_result['path']
            ];
        }
        
        // Log successful application
        error_log("Job application submitted successfully - ID: $application_id, Name: " . $application_data['applicant_name'] . ", Position: " . $application_data['job_title']);
        
    } else {
        $response['message'] = "Failed to save your application. Please try again.";
        
        // Clean up uploaded file if database save failed
        if (file_exists($upload_result['path'])) {
            unlink($upload_result['path']);
        }
    }
    
} catch (Exception $e) {
    error_log("Job application error: " . $e->getMessage());
    $response['message'] = "An unexpected error occurred. Please try again later.";
    
    // Clean up uploaded file if there was an error
    if (isset($upload_result) && $upload_result['success'] && file_exists($upload_result['path'])) {
        unlink($upload_result['path']);
    }
}

echo json_encode($response);
?>
