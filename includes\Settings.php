<?php
require_once 'Database.php';

class Settings {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Get all settings by section
     * @param string|null $section Optional section name to filter by
     * @return array Settings data
     */
    public function getAllSettings($section = null) {
        try {
            if ($section) {
                return $this->db->select(
                    "SELECT * FROM settings WHERE section = ? ORDER BY display_order ASC",
                    [$section]
                );
            } else {
                return $this->db->select(
                    "SELECT * FROM settings ORDER BY section, display_order ASC"
                );
            }
        } catch (Exception $e) {
            error_log("Error getting all settings: " . $e->getMessage(), 3, "../logs/error.log");

            // Try direct query as fallback
            try {
                $conn = $this->db->getConnection();

                if ($section) {
                    $stmt = $conn->prepare("SELECT * FROM settings WHERE section = ? ORDER BY display_order ASC");
                    $stmt->execute([$section]);
                } else {
                    $stmt = $conn->prepare("SELECT * FROM settings ORDER BY section, display_order ASC");
                    $stmt->execute();
                }

                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (Exception $directError) {
                error_log("Direct query error for all settings: " . $directError->getMessage(), 3, "../logs/error.log");
                return [];
            }
        }
    }

    /**
     * Get grouped settings for all sections
     * @return array Settings grouped by section
     */
    public function getGroupedSettings() {
        try {
            $settings = $this->getAllSettings();
            $grouped = [];

            foreach ($settings as $setting) {
                if (!isset($grouped[$setting['section']])) {
                    $grouped[$setting['section']] = [];
                }
                $grouped[$setting['section']][$setting['setting_key']] = $setting['setting_value'];
            }

            return $grouped;
        } catch (Exception $e) {
            error_log("Error getting grouped settings: " . $e->getMessage(), 3, "../logs/error.log");

            // Try direct query as fallback
            try {
                $conn = $this->db->getConnection();
                $stmt = $conn->prepare("SELECT * FROM settings ORDER BY section, display_order ASC");
                $stmt->execute();
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

                $grouped = [];
                foreach ($results as $setting) {
                    if (!isset($grouped[$setting['section']])) {
                        $grouped[$setting['section']] = [];
                    }
                    $grouped[$setting['section']][$setting['setting_key']] = $setting['setting_value'];
                }

                return $grouped;
            } catch (Exception $directError) {
                error_log("Direct query error for grouped settings: " . $directError->getMessage(), 3, "../logs/error.log");
                return [];
            }
        }
    }

    /**
     * Get a specific setting value
     * @param string $key Setting key
     * @param string|null $default Default value if setting doesn't exist
     * @return string Setting value
     */
    public function get($key, $default = null) {
        try {
            $setting = $this->db->selectOne(
                "SELECT setting_value FROM settings WHERE setting_key = ?",
                [$key]
            );

            return $setting ? $setting['setting_value'] : $default;
        } catch (Exception $e) {
            error_log("Error getting setting: " . $e->getMessage(), 3, "../logs/error.log");

            // Try direct query as fallback
            try {
                $conn = $this->db->getConnection();
                $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
                $stmt->execute([$key]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                return $result ? $result['setting_value'] : $default;
            } catch (Exception $directError) {
                error_log("Direct query error: " . $directError->getMessage(), 3, "../logs/error.log");
                return $default;
            }
        }
    }

    /**
     * Update or create settings
     * @param string $section Section name
     * @param array $data Settings data to update
     * @return bool Success status
     */
    public function updateSettings($section, $data) {
        try {
            $this->db->getConnection()->beginTransaction();

            foreach ($data as $key => $value) {
                // Check if the setting exists
                $exists = $this->db->selectOne(
                    "SELECT id FROM settings WHERE section = ? AND setting_key = ?",
                    [$section, $key]
                );

                if ($exists) {
                    // Update existing setting
                    try {
                        $this->db->update(
                            'settings',
                            ['setting_value' => $value],
                            'section = ? AND setting_key = ?',
                            [$section, $key]
                        );
                    } catch (Exception $updateError) {
                        error_log("Error updating setting: " . $updateError->getMessage(), 3, "../logs/error.log");
                        // Try direct query as fallback
                        $conn = $this->db->getConnection();
                        $stmt = $conn->prepare("UPDATE settings SET setting_value = ? WHERE section = ? AND setting_key = ?");
                        $stmt->execute([$value, $section, $key]);
                    }
                } else {
                    // Insert new setting
                    try {
                        $this->db->insert('settings', [
                            'section' => $section,
                            'setting_key' => $key,
                            'setting_value' => $value,
                            'display_order' => 0
                        ]);
                    } catch (Exception $insertError) {
                        error_log("Error inserting setting: " . $insertError->getMessage(), 3, "../logs/error.log");
                        // Try direct query as fallback
                        $conn = $this->db->getConnection();
                        $stmt = $conn->prepare("INSERT INTO settings (section, setting_key, setting_value, display_order) VALUES (?, ?, ?, 0)");
                        $stmt->execute([$section, $key, $value]);
                    }
                }
            }

            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error updating settings: " . $e->getMessage(), 3, "../logs/error.log");
            throw $e;
        }
    }

    /**
     * Load default settings if no settings exist
     * @return bool Success status
     */
    public function loadDefaultSettings() {
        // Check if settings already exist
        $existingSettings = $this->db->selectOne("SELECT COUNT(*) as count FROM settings");

        if ($existingSettings && $existingSettings['count'] > 0) {
            return false; // Don't overwrite existing settings
        }

        $defaultSettings = [
            'site' => [
                'site_name' => 'Doctors At Door Step',
                'site_tagline' => 'Healthcare at your doorstep',
                'site_description' => 'We provide quality healthcare services at your home, making medical care accessible and convenient.',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+977 ***********',
                'address' => 'khursanitar marg, Kathmandu, Nepal',
                'working_hours' => '9:00 AM - 8:00 PM, All Days',
                'logo' => 'images/logo.png',
                'favicon' => 'images/favicon.ico'
            ],
            'social' => [
                'facebook' => 'https://facebook.com/doctorsatdoorstep',
                'tiktok' => 'https://www.tiktok.com/@doctorsatdoorstep21',
                'instagram' => 'https://instagram.com/doctorsatdoorstep',
                'linkedin' => 'https://linkedin.com/company/doctorsatdoorstep',
                'youtube' => ''
            ],
            'booking' => [
                'min_booking_notice' => '24',
                'max_booking_advance' => '30',
                'booking_interval' => '60',
                'working_days' => 'all',
                'working_hours_start' => '09:00',
                'working_hours_end' => '20:00',
                'allow_same_day_booking' => '1',
                'require_phone' => '1',
                'require_address' => '1'
            ],
            'email' => [
                'smtp_host' => 'smtp.gmail.com',
                'smtp_port' => '587',
                'smtp_username' => '<EMAIL>',
                'smtp_password' => 'bnjq miad atej kfot',
                'smtp_encryption' => 'tls',
                'from_email' => '<EMAIL>',
                'from_name' => 'Doctors At Door Step',
                'admin_notification_email' => '<EMAIL>'
            ],
            'payment' => [
                'currency' => 'NPR',
                'currency_symbol' => 'Rs.',
                'payment_methods' => 'cash,esewa,khalti',
                'tax_rate' => '13',
                'enable_online_payment' => '1'
            ],
            'system' => [
                'timezone' => 'Asia/Kathmandu',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i',
                'enable_registration' => '1',
                'enable_testimonials' => '1',
                'maintenance_mode' => '0',
                'debug_mode' => '0'
            ]
        ];

        try {
            $this->db->getConnection()->beginTransaction();
            $displayOrder = 0;

            foreach ($defaultSettings as $section => $settings) {
                foreach ($settings as $key => $value) {
                    $this->db->insert('settings', [
                        'section' => $section,
                        'setting_key' => $key,
                        'setting_value' => $value,
                        'display_order' => $displayOrder++
                    ]);
                }
            }

            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error loading default settings: " . $e->getMessage(), 3, "../logs/error.log");
            return false;
        }
    }
}