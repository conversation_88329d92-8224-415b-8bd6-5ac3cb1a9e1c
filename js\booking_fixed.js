// Validate date to ensure it's not in the past
function validateDate(dateInput) {
    const selectedDate = new Date(dateInput.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
        alert('Please select a future date');
        dateInput.value = '';
        return false;
    }
    return true;
}

// Initialize duration options with default values
function initializeDurationOptions() {
    const durationSelect = document.getElementById('duration');
    if (!durationSelect) return;

    // Clear existing options
    durationSelect.innerHTML = '<option value="">Select duration...</option>';

    // Add default duration options - only 12 hours and 24 hours
    const defaultDurations = [
        ['12-hours', '12 Hours'],
        ['24-hours', '24 Hours']
    ];

    defaultDurations.forEach(([value, text]) => {
        const option = new Option(text, value);
        durationSelect.add(option);
    });
}

// Multi-step form functions
function initMultiStepForm() {
    const steps = document.querySelectorAll('.step');
    const nextBtns = document.querySelectorAll('.next-btn');
    const prevBtns = document.querySelectorAll('.prev-btn');
    const submitBtn = document.querySelector('.submit-btn');
    let currentStep = 0;

    if (steps.length === 0) return;

    function showStep(stepIndex) {
        steps.forEach((step, index) => {
            step.classList.toggle('active', index === stepIndex);
        });

        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            const progress = ((stepIndex + 1) / steps.length) * 100;
            progressBar.style.width = progress + '%';
        }

        updateStepNumbers(stepIndex);
    }

    function updateStepNumbers(currentStepIndex) {
        const stepNumbers = document.querySelectorAll('.step-number');
        stepNumbers.forEach((number, index) => {
            number.classList.remove('active', 'completed');
            if (index < currentStepIndex) {
                number.classList.add('completed');
            } else if (index === currentStepIndex) {
                number.classList.add('active');
            }
        });
    }

    function validateCurrentStep() {
        const currentStepElement = steps[currentStep];
        const requiredFields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            field.classList.remove('error');
            if (!field.value.trim()) {
                field.classList.add('error');
                isValid = false;
            }
        });

        if (!isValid) {
            alert('Please fill in all required fields');
        }

        return isValid;
    }

    nextBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            if (validateCurrentStep() && currentStep < steps.length - 1) {
                currentStep++;
                showStep(currentStep);
                updateSummary();
            }
        });
    });

    prevBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep > 0) {
                currentStep--;
                showStep(currentStep);
            }
        });
    });

    showStep(0);
}

function updateSummary() {
    const summaryElements = {
        'summaryName': () => {
            const firstName = document.getElementById('firstName')?.value || '';
            const lastName = document.getElementById('lastName')?.value || '';
            return `${firstName} ${lastName}`.trim();
        },
        'summaryEmail': () => document.getElementById('email')?.value || '',
        'summaryPhone': () => document.getElementById('phone')?.value || '',
        'summaryService': () => {
            const serviceSelect = document.getElementById('service');
            return serviceSelect ? serviceSelect.options[serviceSelect.selectedIndex]?.text || '' : '';
        },
        'summaryCareType': () => {
            const careTypeSelect = document.getElementById('careType');
            return careTypeSelect ? careTypeSelect.options[careTypeSelect.selectedIndex]?.text || '' : '';
        },
        'summaryDate': () => document.getElementById('preferredDate')?.value || '',
        'summaryTime': () => {
            const timeSelect = document.getElementById('preferredTime');
            return timeSelect ? timeSelect.options[timeSelect.selectedIndex]?.text || '' : '';
        },
        'summaryDuration': () => {
            const durationSelect = document.getElementById('duration');
            return durationSelect ? durationSelect.options[durationSelect.selectedIndex]?.text || '' : '';
        }
    };

    Object.entries(summaryElements).forEach(([id, getValue]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = getValue();
        }
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Booking.js loaded successfully');
    
    // Check if required elements exist
    const bookingForm = document.getElementById('bookingForm');
    const dateInput = document.getElementById('preferredDate');
    
    if (!bookingForm) {
        console.error('Booking form not found!');
        return;
    }
    
    if (!dateInput) {
        console.error('Date input not found!');
        return;
    }
    
    console.log('Required elements found, proceeding with initialization');
    
    // Date initialization
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;

    dateInput.addEventListener('change', function() {
        validateDate(this);
    });

    // Initialize duration options with default values
    initializeDurationOptions();

    // Multi-step form initialization
    initMultiStepForm();

    // Handle form submission
    console.log('Attaching submit event handler to booking form');
    
    bookingForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        console.log('Form submission intercepted successfully');

        // Form validation
        const requiredFields = [
            'firstName', 'lastName', 'email', 'phone',
            'service', 'careType', 'preferredDate', 'preferredTime',
            'duration', 'emergencyName', 'emergencyPhone', 'relationship'
        ];

        let isValid = true;
        const formData = new FormData(this);
        
        console.log('Form data collected:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }

        // Check required fields
        requiredFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.classList.remove('error');
                if (!formData.get(field) || formData.get(field).trim() === '') {
                    element.classList.add('error');
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            alert('Please fill in all required fields');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.get('email'))) {
            alert('Please enter a valid email address');
            document.getElementById('email').classList.add('error');
            return;
        }

        // Phone validation
        const phoneRegex = /^\+?[\d\s-]{10,}$/;
        if (!phoneRegex.test(formData.get('phone'))) {
            alert('Please enter a valid phone number');
            document.getElementById('phone').classList.add('error');
            return;
        }

        // Date validation
        const selectedDate = new Date(formData.get('preferredDate'));
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            alert('Please select a future date');
            document.getElementById('preferredDate').classList.add('error');
            return;
        }

        // Submit form via AJAX
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = 'Processing...';

        // Get the current base URL to ensure correct path
        const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
        const processUrl = baseUrl + 'process_booking.php';
        
        console.log('Submitting to URL:', processUrl);
        
        fetch(processUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers.get('content-type'));

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Get response text first to debug
            return response.text().then(text => {
                console.log('Raw response:', text);

                // Check if response looks like JSON
                if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('Invalid JSON response: ' + e.message);
                    }
                } else {
                    throw new Error('Server returned HTML instead of JSON. Response: ' + text.substring(0, 200));
                }
            });
        })
        .then(data => {
            submitButton.disabled = false;
            submitButton.innerHTML = 'Schedule Appointment';

            console.log('Booking response:', data); // Debug logging

            if (data.success) {
                // Update reference number and show success modal
                const referenceElement = document.getElementById('bookingReference');
                if (referenceElement) {
                    referenceElement.textContent = data.reference;
                }
                
                const successModal = document.getElementById('bookingSuccessModal');
                if (successModal) {
                    successModal.style.display = 'block';
                }

                // Reset form
                this.reset();

                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });

                // Log success for debugging
                console.log('Booking successful:', data.reference);
            } else {
                // Show the error message from server
                const errorMessage = data.message || 'An error occurred. Please try again.';
                alert(errorMessage);
                console.error('Booking failed:', data);
            }
        })
        .catch(error => {
            submitButton.disabled = false;
            submitButton.innerHTML = 'Schedule Appointment';

            console.error('Booking error details:', error);

            // More specific error messages
            let errorMessage = 'An error occurred. Please try again later.';
            if (error.message.includes('HTTP error')) {
                errorMessage = 'Server error. Please contact support if the problem persists.';
            } else if (error.message.includes('JSON')) {
                errorMessage = 'Invalid server response. Please contact support.';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error. Please check your internet connection and try again.';
            }

            alert(errorMessage);
            console.error('Booking submission failed:', error.message);
        });
    });

    // Service selection handler
    const serviceSelect = document.getElementById('service');
    if (serviceSelect) {
        serviceSelect.addEventListener('change', function() {
            const service = this.value;
            const durationSelect = document.getElementById('duration');

            // Save the currently selected value if any
            const currentValue = durationSelect.value;

            // Reset duration options
            durationSelect.innerHTML = '<option value="">Select duration...</option>';

            // Add duration options - only 12 hours and 24 hours for all services
            const durations = [
                ['12-hours', '12 Hours'],
                ['24-hours', '24 Hours']
            ];

            durations.forEach(([value, text]) => {
                const option = new Option(text, value);
                durationSelect.add(option);
            });

            // Try to restore the previously selected value if it exists in the new options
            if (currentValue) {
                // Check if the option exists in the new list
                const optionExists = Array.from(durationSelect.options).some(option => option.value === currentValue);
                if (optionExists) {
                    durationSelect.value = currentValue;
                }
            }
        });
    }

    // Care type change handler
    const careTypeSelect = document.getElementById('careType');
    if (careTypeSelect) {
        careTypeSelect.addEventListener('change', function() {
            const careType = this.value;
            const durationSelect = document.getElementById('duration');

            // For recurring care, we might want to handle duration differently
            if (careType === 'recurring') {
                // Instead of disabling, we'll just add a note
                const durationLabel = durationSelect.previousElementSibling;
                if (durationLabel && durationLabel.tagName === 'LABEL') {
                    if (!durationLabel.querySelector('.note')) {
                        const note = document.createElement('span');
                        note.classList.add('note');
                        note.textContent = ' (per visit)';
                        note.style.fontSize = '0.8em';
                        note.style.color = '#666';
                        durationLabel.appendChild(note);
                    }
                }
            } else {
                // Remove the note if it exists
                const durationLabel = durationSelect.previousElementSibling;
                if (durationLabel && durationLabel.tagName === 'LABEL') {
                    const note = durationLabel.querySelector('.note');
                    if (note) {
                        note.remove();
                    }
                }
            }
        });
    }

    // Error highlighting for inputs
    document.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('focus', function() {
            this.classList.remove('error');
        });
    });

    // Modal close handlers
    const closeButton = document.querySelector('.close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            const successModal = document.getElementById('bookingSuccessModal');
            if (successModal) {
                successModal.style.display = 'none';
            }
        });
    }

    // Character counter for textareas
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.addEventListener('input', function() {
            const maxLength = this.getAttribute('maxlength') || 500;
            const remaining = maxLength - this.value.length;
            const counter = this.nextElementSibling;

            if (!counter || !counter.classList.contains('char-counter')) {
                const counterElem = document.createElement('div');
                counterElem.classList.add('char-counter');
                counterElem.textContent = `${remaining} characters remaining`;
                this.parentNode.insertBefore(counterElem, this.nextSibling);
            } else {
                counter.textContent = `${remaining} characters remaining`;
            }
        });
    });
});

// Modal close on outside click
window.addEventListener('click', function(e) {
    const modal = document.getElementById('bookingSuccessModal');
    if (e.target === modal) {
        modal.style.display = 'none';
    }
});
