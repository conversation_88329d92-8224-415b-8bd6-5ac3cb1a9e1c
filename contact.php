<?php include 'includes/header.php'; ?>

<!-- <PERSON> Banner -->
<section class="page-hero contact-hero" data-aos="fade-up">
    <div class="container">
        <div class="breadcrumb">
            <a href="index.php">Home</a>
            <span class="separator">/</span>
            <span>Contact Us</span>
        </div>
        <h1>Let's Talk – We're Here to Help</h1>
        <p>Reach out to us for any questions about our healthcare services</p>
    </div>
    <div class="scroll-hint">
        <span>Get in touch</span>
        <i class="fas fa-chevron-down"></i>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section" data-aos="fade-up">
    <div class="container">
        <div class="contact-grid">
            <!-- Left Column: Form and Map -->
            <div class="contact-form-map-wrapper">
                <!-- Contact Form -->
                <div class="contact-form-wrapper" data-aos="fade-right">
                    <h2>Send Us a Message</h2>
                    <p>Fill out the form below and we'll get back to you as soon as possible.</p>

                    <form action="https://formspree.io/f/xqabgezq" method="POST">
                        <div class="form-map-grid">
                            <div class="form-content">
                                <div class="form-group">
                                    <label for="name">Full Name *</label>
                                    <input type="text" id="name" name="name" required>
                                </div>

                                <div class="form-group">
                                    <label for="email">Email Address *</label>
                                    <input type="email" id="email" name="email" required>
                                </div>

                                <div class="form-group">
                                    <label for="phone">Phone Number *</label>
                                    <input type="tel" id="phone" name="phone" required>
                                </div>

                                <div class="form-group">
                                    <label for="subject">Subject *</label>
                                    <input type="text" id="subject" name="subject" required>
                                </div>

                                <div class="form-group">
                                    <label for="message">Your Message *</label>
                                    <textarea id="message" name="message" rows="6" required></textarea>
                                </div>

                                <div class="form-group">
                                    <div class="g-recaptcha" data-sitekey="6LeMkPwqAAAAAF2mOmW1N1OpEy6en8GGka3YusjX"></div>
                                </div>

                                <button type="submit" class="btn btn-primary">Send Message</button>
                            </div>

                            <!-- Map Section (Beside Form) -->
                            <div class="contact-map-wrapper" data-aos="fade-up">
                                <iframe
                                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14126.113068092644!2d85.31335149652614!3d27.731847766976546!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39eb191729f1ec37%3A0xc877970b4f5d4435!2sKathmandu%2044600!5e0!3m2!1sen!2snp!4v1742543533941!5m2!1sen!2snp"
                                    width="100%"
                                    height="100%"
                                    style="border:0;"
                                    allowfullscreen=""
                                    loading="lazy"
                                    referrerpolicy="no-referrer-when-downgrade">
                                </iframe>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="contact-info-wrapper" data-aos="fade-left">
                <div class="contact-info-box">
                    <h2>Contact Information</h2>
                    <p>Feel free to reach out through any of the following ways:</p>

                    <div class="contact-details">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <div class="contact-text">
                                <h3>Phone</h3>
                                <p><a href="tel:<?php echo htmlspecialchars($settingsHandler->get('contact_phone', '+977 986-0102404')); ?>"><?php echo htmlspecialchars($settingsHandler->get('contact_phone', '+977 986-0102404')); ?></a></p>
                                <p class="text-muted">Available 24/7 for emergencies</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <div class="contact-text">
                                <h3>Email</h3>
                                <p><a href="mailto:<?php echo htmlspecialchars($settingsHandler->get('contact_email', '<EMAIL>')); ?>"><?php echo htmlspecialchars($settingsHandler->get('contact_email', '<EMAIL>')); ?></a></p>
                                <p class="text-muted">We'll respond within 24 hours</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <i class="fas fa-location-dot"></i>
                            <div class="contact-text">
                                <h3>Office Location</h3>
                                <p><?php echo htmlspecialchars($settingsHandler->get('address', 'khursanitar marg, Kathmandu, Nepal')); ?></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <div class="contact-text">
                                <h3>Office Hours</h3>
                                <p><?php echo htmlspecialchars($settingsHandler->get('working_hours', '9:00 AM - 8:00 PM, All Days')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    <div class="contact-social">
                        <h3>Follow Us</h3>
                        <div class="social-links">
                            <?php
                            // Get social media links from settings
                            $facebook = $settingsHandler->get('facebook', '');
                            $tiktok = $settingsHandler->get('tiktok', '');
                            $instagram = $settingsHandler->get('instagram', '');
                            $linkedin = $settingsHandler->get('linkedin', '');
                            $youtube = $settingsHandler->get('youtube', '');

                            if (!empty($facebook)):
                            ?>
                            <a href="<?php echo htmlspecialchars($facebook); ?>" class="social-link" target="_blank"><i class="fab fa-facebook-f"></i></a>
                            <?php endif;
                            if (!empty($tiktok)):
                            ?>
                            <a href="<?php echo htmlspecialchars($tiktok); ?>" class="social-link" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M9 0h1.98c.144.715.54 1.617 1.235 2.512C12.895 3.389 13.797 4 15 4v2c-1.753 0-3.07-.814-4-1.829V11a5 5 0 1 1-5-5v2a3 3 0 1 0 3 3V0Z"/>
                                </svg>
                            </a>
                            <?php endif;
                            if (!empty($instagram)):
                            ?>
                            <a href="<?php echo htmlspecialchars($instagram); ?>" class="social-link" target="_blank"><i class="fab fa-instagram"></i></a>
                            <?php endif;
                            if (!empty($linkedin)):
                            ?>
                            <a href="<?php echo htmlspecialchars($linkedin); ?>" class="social-link" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                            <?php endif;
                            if (!empty($youtube)):
                            ?>
                            <a href="<?php echo htmlspecialchars($youtube); ?>" class="social-link" target="_blank"><i class="fab fa-youtube"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="contact-faq" data-aos="fade-up">
    <div class="container">
        <h2>Common Questions</h2>
        <div class="faq-accordion">
            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>How quickly will you respond to my inquiry?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>We aim to respond to all inquiries within 24 hours during business days. For urgent matters, please call our emergency hotline.</p>
                </div>
            </div>

            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>Can I schedule a consultation before committing?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>Yes, we offer free initial consultations to discuss your needs and how we can help. You can schedule one through our booking page or by calling us directly.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Emergency Contact -->
<section class="emergency-contact" data-aos="fade-up">
    <div class="container">
        <div class="emergency-content">
            <div class="emergency-icon">
                <i class="fas fa-phone-volume"></i>
            </div>
            <div class="emergency-text">
                <h2>24/7 Emergency Contact</h2>
                <p>For urgent medical assistance or care needs</p>
                <a href="tel:<?php echo htmlspecialchars($settingsHandler->get('contact_phone', '+977 986-0102404')); ?>" class="emergency-phone"><?php echo htmlspecialchars($settingsHandler->get('contact_phone', '+977 986-0102404')); ?></a>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for form validation -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Basic form validation
    let name = document.getElementById('name').value;
    let email = document.getElementById('email').value;
    let phone = document.getElementById('phone').value;
    let message = document.getElementById('message').value;

    if (!name || !email || !phone || !message) {
        alert('Please fill in all required fields.');
        return;
    }

    // Email validation
    let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('Please enter a valid email address.');
        return;
    }

    // Phone validation
    let phoneRegex = /^\+?[\d\s-]{10,}$/;
    if (!phoneRegex.test(phone)) {
        alert('Please enter a valid phone number.');
        return;
    }

    // If validation passes, submit the form
    this.submit();
});
</script>

<?php include 'includes/footer.php'; ?>
