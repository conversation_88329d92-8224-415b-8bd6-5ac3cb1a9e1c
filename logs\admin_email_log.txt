# Admin Email Log
# This file logs admin notification emails for development purposes
# Production emails are sent directly to recipients
=== 2025-06-15 15:09:15 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615F59C11
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="6fdfc29d459fa104b50507e4807947ae"

Body: New booking request received:

Reference: BK20250615F59C11

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: +977 **********

SERVICE DETAILS
Service: Home Medical Care
Care Type: General Consultation
Date: 2025-06-16
Time: morning
Duration: 2 hours

MEDICAL INFORMATION
Medical Condition:
Test condition

Special Requirements:
Test requirements

EMERGENCY CONTACT
Name: Emergency Contact
Phone: +977 **********
Relationship: Family

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:35:59 ===
To: <EMAIL>
Subject: New Booking Request - BK2025061533257E
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="e0924a6e9b7a2405eae8464c3c89ad59"

Body: New booking request received:

Reference: BK2025061533257E

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:36:57 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615D1F379
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="93ca879deeecab5b7a5bb0fc9547452e"

Body: New booking request received:

Reference: BK20250615D1F379

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:37:19 ===
To: <EMAIL>
Subject: New Booking Request - BK2025061531CBE5
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="03878011f587c3039e31fa24f1c52519"

Body: New booking request received:

Reference: BK2025061531CBE5

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:38:31 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615B9308F
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="e68ab891d0501c3f0874b845ab6ff941"

Body: New booking request received:

Reference: BK20250615B9308F

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:38:33 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615D3DFC9
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="66b9d003e6c5e4a59f1ce77dc7b6fe13"

Body: New booking request received:

Reference: BK20250615D3DFC9

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:38:34 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615E7053A
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="2d0dfd9c76df0a314d4e5c8d6f7afefd"

Body: New booking request received:

Reference: BK20250615E7053A

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:38:35 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615F682F0
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="fe3a14baafe8d3f496e1c6efa74dbe64"

Body: New booking request received:

Reference: BK20250615F682F0

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:38:35 ===
To: <EMAIL>
Subject: New Booking Request - BK20250615FE1C55
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="fe3a14baafe8d3f496e1c6efa74dbe64"

Body: New booking request received:

Reference: BK20250615FE1C55

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 15:38:41 ===
To: <EMAIL>
Subject: New Booking Request - BK202506155B102A
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="13bf26d2eff4e4316cc0b9f9c5fb013b"

Body: New booking request received:

Reference: BK202506155B102A

PATIENT INFORMATION
Name: Test User
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: General Consultation
Care Type: routine
Date: 2025-06-20
Time: morning
Duration: 30 minutes

MEDICAL INFORMATION
Medical Condition:


Special Requirements:


EMERGENCY CONTACT
Name: Emergency Contact
Phone: **********
Relationship: spouse

Please log in to the admin dashboard to manage this booking.

=== 2025-06-15 16:28:00 ===
To: <EMAIL>
Subject: New Booking Request - BK84180168
Headers: MIME-Version: 1.0
From: Doctors At Door Step System <<EMAIL>>
Reply-To: <EMAIL>
X-Mailer: PHP/8.2.12
Content-Type: multipart/alternative; boundary="7033d63cf9c39ce5139e8b9832c9092d"

Body: New booking request received:

Reference: BK84180168

PATIENT INFORMATION
Name: Sarthak KC
Email: <EMAIL>
Phone: **********

SERVICE DETAILS
Service: home-nursing
Care Type: one-time
Date: 2025-07-01
Time: afternoon
Duration: 12-hours

MEDICAL INFORMATION
Medical Condition:
ihoi

Special Requirements:
ojoij

EMERGENCY CONTACT
Name: ;mm
Phone: 561650
Relationship: ji

Please log in to the admin dashboard to manage this booking.

