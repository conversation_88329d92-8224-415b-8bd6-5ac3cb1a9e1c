/* Blog Page Styles */

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(rgba(44, 123, 229, 0.9), rgba(44, 123, 229, 0.9)), 
                url('../images/blog-hero.jpg') center/cover;
}

.blog-hero h1 {
    color: var(--white);
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.blog-hero p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
}

/* Blog Section */
.blog-section {
    padding: 80px 0;
    background: var(--white);
}

.blog-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

/* Blog Main Content */
.blog-main {
    width: 100%;
}

/* Dynamic Masonry Layout */
.blog-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
    align-items: start;
}

/* Dynamic Blog Cards */
.blog-card {
    background: var(--white);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    break-inside: avoid;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

/* Dynamic Image Container */
.blog-image {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 200px;
}

.blog-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
    z-index: 2;
}

.blog-card:hover .blog-image::before {
    transform: translateX(100%);
}

.blog-image img {
    width: 100%;
    height: auto;
    min-height: 200px;
    max-height: 350px;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1 !important;
    transform: scale(1);
    display: block;
}

.blog-image img.loaded {
    opacity: 1;
    transform: scale(1);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

/* Dynamic Placeholder with Animation */
.placeholder-image {
    width: 100%;
    min-height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 2.5rem;
    position: relative;
    overflow: hidden;
}

.placeholder-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(0,0,0,0.02) 10px,
        rgba(0,0,0,0.02) 20px
    );
    animation: shimmer 3s linear infinite;
}

.placeholder-image i {
    position: relative;
    z-index: 2;
    margin-bottom: 10px;
    animation: pulse 2s ease-in-out infinite;
}

.placeholder-image::after {
    content: 'No Image Available';
    position: relative;
    z-index: 2;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.7;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%); }
    100% { transform: translateX(100%) translateY(100%); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Loading Skeleton */
.blog-image.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Dynamic Content Area */
.blog-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background: var(--white);
    transition: all 0.3s ease;
}

.blog-card:hover .blog-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* Content Preview Overlay */
.blog-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.9) 70%, rgba(255,255,255,1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.blog-card:hover .blog-content::before {
    opacity: 1;
}

/* Enhanced Content Preview */
.content-preview {
    position: absolute;
    bottom: 25px;
    left: 25px;
    right: 25px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 15px;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-card:hover .content-preview {
    transform: translateY(0);
    opacity: 1;
}

.content-preview p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--secondary-color);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.blog-date, .blog-category {
    display: flex;
    align-items: center;
    gap: 5px;
}

.blog-category a {
    color: var(--primary-color);
    transition: var(--transition);
}

.blog-category a:hover {
    text-decoration: underline;
}

.blog-content h2 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    line-height: 1.4;
}

.blog-content h2 a {
    color: var(--dark-color);
    transition: var(--transition);
}

.blog-content h2 a:hover {
    color: var(--primary-color);
}

.blog-content p {
    color: var(--secondary-color);
    margin-bottom: 20px;
    flex-grow: 1;
}

.read-more {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-top: auto;
    transition: var(--transition);
}

.read-more i {
    font-size: 0.8rem;
    transition: var(--transition);
}

.read-more:hover {
    color: var(--primary-dark);
}

.read-more:hover i {
    transform: translateX(5px);
}

/* No Posts Found */
.no-posts {
    text-align: center;
    padding: 50px 20px;
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
}

.no-posts h2 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.no-posts p {
    color: var(--secondary-color);
    max-width: 500px;
    margin: 0 auto;
}

/* Blog Sidebar */
.blog-sidebar {
    width: 100%;
}

.sidebar-widget {
    background: var(--white);
    border-radius: var(--card-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.sidebar-widget h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--gray-200);
    position: relative;
}

.sidebar-widget h3:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Search Widget */
.search-widget {
    padding: 0;
    overflow: hidden;
}

.search-form {
    display: flex;
}

.search-form input {
    flex: 1;
    padding: 15px;
    border: none;
    background: var(--white);
    font-family: inherit;
}

.search-form input:focus {
    outline: none;
}

.search-form button {
    width: 50px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.search-form button:hover {
    background: var(--primary-dark);
}

/* Category List */
.category-list {
    list-style: none;
}

.category-list li {
    margin-bottom: 12px;
    border-bottom: 1px dashed var(--gray-200);
    padding-bottom: 12px;
}

.category-list li:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.category-list a {
    display: flex;
    justify-content: space-between;
    color: var(--dark-color);
    transition: var(--transition);
}

.category-list a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.category-count {
    color: var(--secondary-color);
}

/* Recent Posts */
.recent-posts {
    list-style: none;
}

.recent-posts li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--gray-200);
}

.recent-posts li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.recent-posts a {
    display: flex;
    gap: 15px;
    color: inherit;
    transition: var(--transition);
}

.recent-posts a:hover {
    color: var(--primary-color);
}

.post-thumbnail {
    width: 80px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    flex-shrink: 0;
}

.post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 1 !important;
    display: block;
}

.post-info {
    flex: 1;
}

.post-info h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.post-date {
    font-size: 0.85rem;
    color: var(--secondary-color);
}

/* Tag Cloud */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag {
    display: inline-block;
    padding: 5px 12px;
    background: var(--gray-100);
    color: var(--secondary-color);
    border-radius: 20px;
    font-size: 0.85rem;
    transition: var(--transition);
}

.tag:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
}

/* CTA Section */
.blog-cta {
    padding: 80px 0;
    background: linear-gradient(rgba(44, 123, 229, 0.9), rgba(44, 123, 229, 0.9)), 
                url('../images/cta-bg.jpg') center/cover;
    text-align: center;
    color: var(--white);
}

.blog-cta h2 {
    color: var(--white);
    margin-bottom: 20px;
    font-size: 2.2rem;
}

.blog-cta p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.1rem;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.blog-cta .btn-outline {
    border-color: var(--white);
    color: var(--white);
}

.blog-cta .btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 50px;
}

.pagination-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 15px;
    background: var(--white);
    color: var(--dark-color);
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
}

.pagination-item:hover {
    background: var(--light-color);
}

.pagination-item.active {
    background: var(--primary-color);
    color: var(--white);
}

.pagination-ellipsis {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

/* Blog Post Page Styles */

/* Blog Post Hero */
.blog-post-hero {
    background: linear-gradient(rgba(26, 43, 60, 0.85), rgba(26, 43, 60, 0.85)), 
                url('../images/blog-post-hero.jpg') center/cover;
    text-align: center;
}

.blog-post-hero .post-meta {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.8);
}

.blog-post-hero .post-meta a {
    color: var(--primary-light);
}

.blog-post-hero .post-meta a:hover {
    text-decoration: underline;
}

/* Blog Post Content */
.blog-post-section {
    padding: 80px 0;
    background: var(--white);
}

.blog-post-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.blog-post-main {
    background: var(--white);
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.post-featured-image {
    width: 100%;
    height: auto;
    max-height: 500px;
    overflow: hidden;
}

.post-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-content {
    padding: 40px;
}

.post-content h2 {
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.post-content h3 {
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.post-content p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--secondary-color);
}

.post-content ul, .post-content ol {
    margin-bottom: 20px;
    margin-left: 20px;
}

.post-content li {
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.post-content blockquote {
    padding: 20px;
    background: var(--light-color);
    border-left: 4px solid var(--primary-color);
    margin: 30px 0;
    font-style: italic;
    color: var(--dark-color);
}

.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
}

.post-content a {
    color: var(--primary-color);
    text-decoration: underline;
}

.post-content a:hover {
    color: var(--primary-dark);
}

/* Post Tags */
.post-tags {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.tags-label {
    font-weight: 600;
    color: var(--dark-color);
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Author Info */
.post-author {
    margin-top: 40px;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    display: flex;
    align-items: center;
    gap: 20px;
}

.author-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0;
}

.author-info h3 {
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.author-info p {
    color: var(--secondary-color);
    margin-bottom: 5px;
}

/* Post Navigation */
.post-navigation {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.post-nav-link {
    flex: 1;
    padding: 20px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    color: var(--dark-color);
    transition: var(--transition);
}

.post-nav-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-5px);
}

.prev-post {
    text-align: left;
}

.next-post {
    text-align: right;
}

.post-nav-label {
    display: block;
    font-size: 0.85rem;
    margin-bottom: 5px;
    opacity: 0.8;
}

.post-nav-title {
    font-weight: 600;
}

/* Comments Section */
.comments-section {
    margin-top: 60px;
}

.comments-section h2 {
    margin-bottom: 30px;
    font-size: 1.8rem;
}

.comment-list {
    margin-bottom: 40px;
}

.comment {
    display: flex;
    gap: 20px;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    margin-bottom: 20px;
}

.comment.reply {
    margin-left: 60px;
    margin-top: 20px;
    background: var(--white);
    border: 1px solid var(--gray-200);
}

.comment-avatar {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.comment-content {
    flex: 1;
}

.comment-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.comment-author {
    margin: 0;
    font-size: 1.1rem;
}

.comment-date {
    font-size: 0.85rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

.comment-text {
    margin-bottom: 15px;
    color: var(--secondary-color);
    line-height: 1.7;
}

.comment-actions {
    display: flex;
    gap: 15px;
}

.reply-button {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 0;
    transition: var(--transition);
}

.reply-button:hover {
    color: var(--primary-dark);
}

.comment-replies {
    margin-top: 20px;
}

/* Comment Form */
.comment-form-container {
    background: var(--white);
    border-radius: var(--card-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    margin-top: 40px;
}

.comment-form-container h3 {
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.comment-success {
    padding: 15px;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-radius: 5px;
    margin-bottom: 20px;
}

.comment-error {
    padding: 15px;
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border-radius: 5px;
    margin-bottom: 20px;
}

.comment-form .form-group {
    margin-bottom: 20px;
}

.comment-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.comment-form input,
.comment-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--gray-200);
    border-radius: 5px;
    font-family: inherit;
    transition: var(--transition);
}

.comment-form input:focus,
.comment-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 123, 229, 0.1);
}

.comment-form .form-actions {
    display: flex;
    gap: 15px;
}

.btn-cancel-reply {
    padding: 10px 20px;
    background: var(--gray-200);
    color: var(--secondary-color);
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-cancel-reply:hover {
    background: var(--gray-300);
}

.btn-submit {
    padding: 10px 25px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-submit:hover {
    background: var(--primary-dark);
}

.reply-form-container {
    margin-top: 20px;
    padding: 20px;
    background: var(--white);
    border-radius: 5px;
    border: 1px solid var(--gray-200);
}

.no-comments {
    text-align: center;
    padding: 30px;
    background: var(--light-color);
    border-radius: var(--card-radius);
    margin-bottom: 30px;
}

/* Related Posts in Sidebar */
.related-posts-widget h3 {
    color: var(--primary-color);
}

/* Media Queries */
@media (max-width: 1200px) {
    .blog-grid,
    .blog-post-grid {
        gap: 30px;
    }
    
    .cta-buttons {
        flex-direction: column;
        max-width: 250px;
        margin: 0 auto;
    }
}

@media (max-width: 991px) {
    .blog-grid,
    .blog-post-grid {
        grid-template-columns: 1fr;
    }
    
    .blog-sidebar {
        order: -1;
    }
    
    .blog-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .post-navigation {
        flex-direction: column;
    }
    
    .post-content {
        padding: 30px;
    }
    
    .comment {
        padding: 20px;
    }
    
    .comment.reply {
        margin-left: 30px;
    }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .blog-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .blog-hero h1 {
        font-size: 2.2rem;
    }

    .blog-hero p {
        font-size: 1rem;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .blog-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .blog-card:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .blog-image img {
        min-height: 180px;
        max-height: 250px;
    }

    .content-preview {
        position: static;
        transform: none;
        opacity: 1;
        background: transparent;
        backdrop-filter: none;
        box-shadow: none;
        padding: 0;
        margin-top: 15px;
    }

    .post-author {
        flex-direction: column;
        text-align: center;
    }

    .author-avatar {
        margin: 0 auto;
    }

    .comment {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .comment-meta {
        justify-content: center;
    }

    .comment-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .blog-section,
    .blog-post-section {
        padding: 50px 0;
    }

    .blog-posts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .blog-image img {
        min-height: 160px;
        max-height: 220px;
    }

    .blog-content {
        padding: 15px;
    }

    .post-content {
        padding: 20px;
    }

    .post-featured-image {
        max-height: 300px;
    }

    .comment-form-container {
        padding: 20px;
    }

    .blog-cta h2 {
        font-size: 1.8rem;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}