<?php
require_once 'includes/Services.php';
require_once 'includes/Database.php';
include 'includes/header.php';

$servicesHandler = new Services();
$services = $servicesHandler->getAllActiveServices();
?>

<!-- Hero Banner -->
<section class="page-hero services-hero" data-aos="fade-up">
    <div class="container">
        <div class="breadcrumb">
            <a href="index.php">Home</a>
            <span class="separator">/</span>
            <span>Services</span>
        </div>
        <h1>Our Health Care Services</h1>
        <p>Comprehensive doorstep medical solutions by qualified doctors tailored to your needs</p>
    </div>
    <div class="scroll-hint">
        <span>Explore our services</span>
        <i class="fas fa-chevron-down"></i>
    </div>
</section>

<!-- Services Grid -->
<section class="services-grid-section" data-aos="fade-up">
    <div class="container">
        <div class="section-header">

        </div>

        <?php if (empty($services)): ?>
            <div class="no-services">
                <p>No services are currently available. Please check back later.</p>
            </div>
        <?php else: ?>
            <div class="services-horizontal-grid">
                <?php foreach ($services as $service): ?>
                    <div class="service-card-horizontal" id="service-<?php echo $service['id']; ?>" data-aos="fade-up">                        <div class="service-card-icon">
                            <i class="fas <?php echo htmlspecialchars($service['icon']); ?>"></i>
                        </div>
                        <div class="service-card-content">
                            <h3><?php echo htmlspecialchars($service['title']); ?></h3>
                            <p><?php
                                // Show only first 120 characters as summary
                                $description = htmlspecialchars($service['description']);
                                if (strlen($description) > 120) {
                                    echo substr($description, 0, 120) . '...';
                                } else {
                                    echo $description;
                                }
                            ?></p>
                            <div class="service-card-meta">
                    <!--
                            <div class="service-duration"><?php echo $servicesHandler->formatDuration($service['duration']); ?></div>
                                                    -->
                            </div>

                            <a href="service-details.php?id=<?php echo $service['id']; ?>" class="service-card-link">
                                Learn More <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>


<!-- FAQ Section -->
<section class="faq-section" data-aos="fade-up">
    <div class="container">
        <h2>Frequently Asked Questions</h2>
        <p class="section-subtitle">Find answers to common questions about our services</p>

        <div class="faq-accordion">
            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>What types of medical services do you offer?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>We offer a comprehensive range of doorstep medical services including doctor consultations, elderly assistance, physical therapy, 24/7 doctor support, and companionship care. Each service is customized to meet your specific needs in Kathmandu.</p>
                </div>
            </div>

            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>How do you select your doctors?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>All our doctors undergo thorough background checks, licensing verification, and extensive training. We only select medical professionals who demonstrate both technical expertise and compassionate care abilities.</p>
                </div>
            </div>

            <div class="faq-item" data-aos="fade-up">
                <div class="faq-question">
                    <h3>What are your service hours?</h3>
                    <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                </div>
                <div class="faq-answer">
                    <p>We provide flexible scheduling options, including 24/7 care services. Our care plans can be adjusted to accommodate both short-term and long-term needs, with services available day or night.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get testimonials data with fixed image paths
require_once 'includes/Testimonials.php';
$testimonialHandler = new Testimonials();
$testimonials = $testimonialHandler->getAllActiveTestimonialsForDisplay();
?>

<!-- Testimonials Section -->
<section class="testimonials">
    <div class="container">
        <div class="section-header">
            <h2>What Our Clients Say</h2>
            <p>Real experiences from families we've helped</p>
        </div>

        <div class="testimonials-slider">
            <!-- Swiper Container -->
            <div class="swiper-container testimonials-swiper">
                <div class="swiper-wrapper">
                    <?php if (empty($testimonials)): ?>
                        <!-- Fallback testimonials with full content -->
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"The care and attention provided by the doctors was exceptional. They made my recovery process so much more comfortable in my own home. The professional approach and genuine concern for my wellbeing really stood out."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Raj Thapa</h4>
                                        <p>Patient</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"Professional medical services at home made all the difference for our elderly father. The doctors were punctual, caring, and highly skilled. We couldn't have asked for better healthcare support."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Sita Sharma</h4>
                                        <p>Family Member</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($testimonials as $testimonial): ?>
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-content">
                                        <p>"<?php echo htmlspecialchars($testimonial['display_content']); ?>"</p>
                                    </div>
                                    <div class="testimonial-author">
                                        <!-- Enhanced image path handling for webserver compatibility -->
                                        <?php if (!empty($testimonial['photo_path'])): ?>
                                            <div class="testimonial-author-image" style="background-image: url('<?php echo htmlspecialchars($testimonial['photo_path']); ?>'); background-size: cover; background-position: center;"></div>
                                        <?php else: ?>
                                            <div class="testimonial-author-image no-image">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="author-info">
                                            <h4><?php echo htmlspecialchars($testimonial['name']); ?></h4>
                                            <p><?php echo htmlspecialchars($testimonial['position']); ?></p>
                                            <?php if ($testimonial['rating'] > 0): ?>
                                            <div class="testimonial-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $testimonial['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
            </div>

            <!-- Add Navigation -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </div>
</section>

<!-- Booking CTA -->
<section class="booking-cta" data-aos="fade-up">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Book a free consultation to discuss your healthcare needs and create a personalized care plan.</p>
            <div class="cta-buttons">
                <a href="booking.php" class="btn btn-primary">Book Consultation</a>
                <a href="contact.php" class="btn btn-outline">Contact Us</a>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Slider Initialization Script -->
<script>
// Testimonials Slider Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize testimonials slider
    if (document.querySelector('.testimonials-swiper')) {
        // Check if Swiper is already loaded
        if (typeof Swiper !== 'undefined') {
            initTestimonialsSlider();
        } else {
            // If Swiper isn't loaded yet, wait for a moment and try again
            setTimeout(function() {
                if (typeof Swiper !== 'undefined') {
                    initTestimonialsSlider();
                } else {
                    console.error('Swiper library not loaded properly');
                }
            }, 1000);
        }
    }
});

function initTestimonialsSlider() {
    // Simple configuration without complex animations
    var testimonialSwiper = new Swiper('.testimonials-swiper', {
        // Optional parameters
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        // Responsive breakpoints
        breakpoints: {
            // when window width is >= 768px
            768: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            // when window width is >= 1024px
            1024: {
                slidesPerView: 2,
                spaceBetween: 30
            }
        },
        // Navigation arrows
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        // Pagination
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
