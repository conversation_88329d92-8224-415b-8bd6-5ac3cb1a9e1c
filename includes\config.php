<?php

// If session hasn't started yet, configure and start it
if (session_status() === PHP_SESSION_NONE && !headers_sent()) {
    // Session configuration
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));

    // Start session
    session_start();
}

// Detect environment - check if we're on localhost or production
// Handle both web requests and CLI
$http_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
$is_localhost = (
    $http_host === 'localhost:8080' ||
    $http_host === 'localhost' ||
    $http_host === '127.0.0.1' ||
    $http_host === '127.0.0.1:8080' ||
    php_sapi_name() === 'cli' // Command line interface
);

// Set debug mode based on environment
define('DEBUG_MODE', $is_localhost);

if (DEBUG_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(E_ERROR | E_PARSE); // Only show critical errors in production
}

// Set timezone
date_default_timezone_set('Asia/Kathmandu');

// Dynamic site URL detection
if ($is_localhost) {
    // For localhost development, use localhost URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
    $site_url = $protocol . '://' . $host;
} else {
    // For production, always use the live domain
    $site_url = 'https://doctorsatdoorstep.com.np';
}

// Site Configuration
define('SITE_NAME', 'Doctors At Your Door Step');
define('SITE_URL', $site_url);
define('ADMIN_EMAIL', '<EMAIL>');
define('SUPPORT_EMAIL', '<EMAIL>');
define('NOREPLY_EMAIL', '<EMAIL>');

/*
=== EMAIL SETUP INSTRUCTIONS ===

To set up email sending for your domain (ddatd.com), you have several options:

OPTION 1: Use Gmail SMTP (Recommended for testing)
- Create a Gmail account for your domain (e.g., <EMAIL>)
- Enable 2-factor authentication
- Generate an "App Password" for your application
- Update the settings below

OPTION 2: Use your hosting provider's SMTP
- Contact your hosting provider (Protozoa) for SMTP settings
- Usually something like mail.yourdomain.com

OPTION 3: Use a service like SendGrid, Mailgun, or Amazon SES
- More reliable for production
- Better deliverability rates

CURRENT SETUP: Update these values with your actual email credentials
*/

// Email Configuration for automatic confirmation emails
// Use different methods for localhost vs production
if ($is_localhost) {
    define('MAIL_METHOD', 'smtp'); // Use Gmail SMTP for localhost testing
    // SMTP Settings for localhost (Gmail)
    define('SMTP_HOST', 'smtp.gmail.com');
    define('SMTP_USERNAME', '<EMAIL>');
    define('SMTP_PASSWORD', 'bnjq miad atej kfot');
    define('SMTP_PORT', 587);
    define('SMTP_SECURE', 'tls');
} else {
    define('MAIL_METHOD', 'smtp'); // Use cPanel SMTP for production
    // Production cPanel SMTP settings
    define('SMTP_HOST', 'mail.doctorsatdoorstep.com.np'); // Your domain's mail server
    define('SMTP_USERNAME', '<EMAIL>'); // Your cPanel email
    define('SMTP_PASSWORD', 'Ss@071424'); // Your actual cPanel email password
    define('SMTP_PORT', 465); // SSL port for your cPanel SMTP
    define('SMTP_SECURE', 'ssl'); // Using SSL for port 465
}

// PHPMailer autoloader - include Composer's autoloader
if (file_exists($_SERVER['DOCUMENT_ROOT'] . '/ddatd/vendor/autoload.php')) {
    require_once $_SERVER['DOCUMENT_ROOT'] . '/ddatd/vendor/autoload.php';
} elseif (file_exists(dirname(__DIR__) . '/vendor/autoload.php')) {
    require_once dirname(__DIR__) . '/vendor/autoload.php';
}

// Database configuration - different for localhost vs production
if ($is_localhost) {
    // Local development settings
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'ddatd_db');
    define('DB_USER', 'root');
    define('DB_PASS', '');
} else {
    // Production settings for Protozoa hosting
    // These need to be updated with your actual Protozoa database credentials
   define('DB_HOST', 'localhost');
define('DB_NAME', 'doctorsa_db');
define('DB_USER', 'doctorsa_admin');  // Using root for development
define('DB_PASS', '8gBS*M,OptO_'); 
}

// Security settings
define('CSRF_TOKEN_TIME', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 900); // 15 minutes

// File upload settings
define('MAX_FILE_SIZE', 5242880); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);
define('UPLOAD_PATH', $_SERVER['DOCUMENT_ROOT'] . '/ddatd/uploads/');

// Booking settings
define('MIN_BOOKING_NOTICE', 24); // Hours
define('MAX_BOOKING_ADVANCE', 90); // Days
define('DEFAULT_BOOKING_DURATION', 2); // Hours

// Error logging
define('ERROR_LOG_FILE', $_SERVER['DOCUMENT_ROOT'] . '/ddatd/logs/error.log');
define('ACCESS_LOG_FILE', $_SERVER['DOCUMENT_ROOT'] . '/ddatd/logs/access.log');

// Utility functions
function sanitize_output($buffer) {
    $search = [
        '/\>[^\S ]+/s',     // Remove whitespaces after tags
        '/[^\S ]+\</s',     // Remove whitespaces before tags
        '/(\s)+/s',         // Shorten multiple whitespace sequences
        '/<!--(.|\s)*?-->/' // Remove HTML comments
    ];

    $replace = ['>', '<', '\\1', ''];

    return preg_replace($search, $replace, $buffer);
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

function validate_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || 
        !isset($_SESSION['csrf_token_time']) ||
        $token !== $_SESSION['csrf_token']) {
        return false;
    }
    
    if (time() - $_SESSION['csrf_token_time'] > CSRF_TOKEN_TIME) {
        return false;
    }
    
    return true;
}

function log_error($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message\n";
    error_log($log_entry, 3, ERROR_LOG_FILE);
}
