<?php
require_once __DIR__ . '/../../includes/Database.php';

class Service {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    public function getAllServices() {
        return $this->db->select("SELECT * FROM services ORDER BY id DESC");
    }

    public function getServiceById($id) {
        return $this->db->selectOne(
            "SELECT * FROM services WHERE id = ?",
            [$id]
        );
    }    public function createService($data) {
        try {
            // Validate required fields
            if (empty($data['title'])) {
                throw new Exception("Service title is required");
            }
            if (empty($data['description'])) {
                throw new Exception("Service description is required");
            }
            if (empty($data['icon']) || trim($data['icon']) === '') {
                throw new Exception("Service icon is required");
            }
            if (!isset($data['price']) || !is_numeric($data['price'])) {
                throw new Exception("Valid service price is required");
            }
            if (empty($data['duration']) || !is_numeric($data['duration'])) {
                throw new Exception("Valid service duration is required");
            }
            if (empty($data['status'])) {
                throw new Exception("Service status is required");
            }
              // Sanitize data
            $serviceData = [
                'title' => trim($data['title']),
                'description' => trim($data['description']),
                'icon' => trim($data['icon']), // Form field and DB field both use 'icon'
                'price' => floatval($data['price']),
                'duration' => $this->formatDuration($data['duration']), // Format duration properly
                'is_active' => ($data['status'] === 'active') ? 1 : 0
            ];

            // Log the data being inserted for debugging
            error_log("Creating service with data: " . print_r($serviceData, true));

            $result = $this->db->insert('services', $serviceData);
            
            if (!$result) {
                throw new Exception("Failed to insert service into database");
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error creating service: " . $e->getMessage());
            error_log("Input data: " . print_r($data, true));
            throw $e;
        }
    }    public function updateService($id, $data) {
        try {
            // Debug: Log all incoming data
            error_log("=== UPDATE SERVICE DEBUG ===");
            error_log("Incoming data: " . print_r($data, true));
            error_log("Icon value: '" . (isset($data['icon']) ? $data['icon'] : 'NOT SET') . "'");
            error_log("Icon empty check: " . (empty($data['icon']) ? 'TRUE' : 'FALSE'));
            
            // Validate required fields (same as create)
            if (empty($data['title'])) {
                throw new Exception("Service title is required");
            }
            if (empty($data['description'])) {
                throw new Exception("Service description is required");
            }
            if (empty($data['icon']) || trim($data['icon']) === '') {
                throw new Exception("Service icon is required");
            }
            if (!isset($data['price']) || !is_numeric($data['price'])) {
                throw new Exception("Valid service price is required");
            }
            if (empty($data['duration']) || !is_numeric($data['duration'])) {
                throw new Exception("Valid service duration is required");
            }
            if (empty($data['status'])) {
                throw new Exception("Service status is required");
            }
            
            // Sanitize data
            $serviceData = [
                'title' => trim($data['title']),
                'description' => trim($data['description']),
                'icon' => trim($data['icon']), // Form field and DB field both use 'icon'
                'price' => floatval($data['price']),
                'duration' => $this->formatDuration($data['duration']), // Format duration properly
                'is_active' => ($data['status'] === 'active') ? 1 : 0
            ];

            // Log the data being updated for debugging
            error_log("Updating service ID $id with data: " . print_r($serviceData, true));

            $this->db->update('services', $serviceData, 'id = ?', [$id]);
            return true;
        } catch (Exception $e) {
            error_log("Error updating service: " . $e->getMessage());
            error_log("Data: " . print_r($data, true));
            throw $e;
        }
    }

    public function deleteService($id) {
        try {
            // Check if service is being used in bookings
            $bookings = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM bookings WHERE service_id = ?",
                [$id]
            );

            if ($bookings['count'] > 0) {
                throw new Exception("Cannot delete service as it has associated bookings");
            }

            $this->db->delete('services', 'id = ?', [$id]);
            return true;
        } catch (Exception $e) {
            log_error("Error deleting service: " . $e->getMessage());
            throw $e;
        }
    }

    public function toggleServiceStatus($id) {
        try {
            $service = $this->getServiceById($id);
            if (!$service) {
                throw new Exception("Service not found");
            }

            $this->db->update(
                'services',
                ['is_active' => $service['is_active'] ? 0 : 1],
                'id = ?',
                [$id]
            );
            return true;
        } catch (Exception $e) {
            log_error("Error toggling service status: " . $e->getMessage());
            throw $e;
        }
    }    public function searchServices($term) {
        return $this->db->select(
            "SELECT * FROM services WHERE title LIKE ? OR description LIKE ? ORDER BY id DESC",
            ["%$term%", "%$term%"]
        );
    }
      /**
     * Convert duration input to minutes (integer) for database storage
     */
    private function formatDuration($duration) {
        $duration = trim($duration);
        
        // If it's already a number, return it as integer (assuming minutes)
        if (is_numeric($duration)) {
            return (int)$duration;
        }
        
        // Parse various duration formats and convert to minutes
        if (preg_match('/(\d+)\s*min/', $duration, $matches)) {
            return (int)$matches[1];
        }
        if (preg_match('/(\d+)\s*hour/', $duration, $matches)) {
            return (int)$matches[1] * 60;
        }
        if (preg_match('/(\d+)-(\d+)\s*hour/', $duration, $matches)) {
            return (int)$matches[1] * 60; // Use the lower bound
        }
        
        // Default fallback - assume it's 60 minutes
        return 60;
    }
    
}
