-- Create services table first
CREATE TABLE IF NOT EXISTS `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `duration` varchar(50) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample services
INSERT INTO `services` (`id`, `title`, `description`, `price`, `duration`, `icon`, `is_active`) VALUES
(1, 'Home Medical Care', 'Professional medical care in the comfort of your home', 2500.00, '2-4 hours', 'fa-stethoscope', 1),
(2, 'Elderly Assistance', 'Compassionate care for elderly patients', 2000.00, '4-8 hours', 'fa-user-nurse', 1),
(3, 'Physical Therapy', 'Rehabilitation services at your doorstep', 1800.00, '1-2 hours', 'fa-walking', 1),
(4, '24/7 Doctor Support', 'Round-the-clock medical assistance', 3500.00, '24 hours', 'fa-user-md', 1),
(5, 'Companionship Care', 'Friendly companionship and basic assistance', 1500.00, '4-8 hours', 'fa-hands-helping', 1);

-- Create bookings table with foreign key reference
CREATE TABLE IF NOT EXISTS `bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(20) DEFAULT NULL,
  `client_name` varchar(100) NOT NULL,
  `client_email` varchar(100) NOT NULL,
  `client_phone` varchar(20) NOT NULL,
  `service_id` int(11) DEFAULT NULL, /* Explicitly nullable */
  `booking_date` date NOT NULL,
  `booking_time` time NOT NULL,
  `address` text NOT NULL,
  `special_requests` text DEFAULT NULL,
  `status` enum('pending','confirmed','completed','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `booking_date` (`booking_date`),
  KEY `reference` (`reference`),
  KEY `service_id` (`service_id`),
  CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create inquiries table
CREATE TABLE IF NOT EXISTS `inquiries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `status` enum('new','in_progress','resolved') NOT NULL DEFAULT 'new',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data into bookings table
INSERT INTO `bookings` (`client_name`, `client_email`, `client_phone`, `service_id`, `booking_date`, `booking_time`, `address`, `special_requests`, `status`) VALUES
('John Doe', '<EMAIL>', '+977 **********', 1, '2025-05-25', '10:00:00', '123 Main St, Kathmandu', 'Please bring blood pressure monitor', 'pending'),
('Jane Smith', '<EMAIL>', '+977 **********', 2, '2025-05-26', '14:30:00', '456 Park Ave, Lalitpur', 'Patient has mobility issues', 'confirmed'),
('Michael Johnson', '<EMAIL>', '+977 **********', 3, '2025-05-27', '09:15:00', '789 Oak Rd, Bhaktapur', 'Need assistance with medication', 'completed'),
('Sarah Williams', '<EMAIL>', '+977 **********', 1, '2025-05-28', '16:00:00', '321 Pine St, Kathmandu', 'Elderly patient needs gentle care', 'pending'),
('Robert Brown', '<EMAIL>', '+977 **********', 2, '2025-05-29', '11:30:00', '654 Maple Ave, Lalitpur', 'Patient has diabetes', 'cancelled');

-- Insert sample data into inquiries table
INSERT INTO `inquiries` (`name`, `email`, `phone`, `subject`, `message`, `status`) VALUES
('Rajesh Kumar', '<EMAIL>', '+977 **********', 'Question about home care services', 'I would like to know more about your home nursing services for my elderly mother. What are your rates and availability?', 'new'),
('Priya Sharma', '<EMAIL>', '+977 **********', 'Availability in Bhaktapur', 'Do you provide services in the Bhaktapur area? I need regular check-ups for my father who recently had surgery.', 'in_progress'),
('Amit Patel', '<EMAIL>', '+977 **********', 'Pricing inquiry', 'Could you please provide me with your pricing details for 24/7 care? I need someone to look after my grandmother.', 'resolved'),
('Sunita Thapa', '<EMAIL>', '+977 **********', 'Emergency services', 'Do you offer emergency medical services? What is the response time in case of an emergency?', 'new'),
('Binod Shrestha', '<EMAIL>', '+977 **********', 'Specialized care for stroke patient', 'My father recently had a stroke and needs specialized care. Do you have staff trained in stroke rehabilitation?', 'in_progress');
