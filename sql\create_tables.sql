-- Create services table first
CREATE TABLE IF NOT EXISTS `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `duration` varchar(50) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample services
INSERT INTO `services` (`id`, `title`, `description`, `price`, `duration`, `icon`, `is_active`) VALUES
(1, 'Home Medical Care', 'Professional medical care in the comfort of your home', 2500.00, '2-4 hours', 'fa-stethoscope', 1),
(2, 'Elderly Assistance', 'Compassionate care for elderly patients', 2000.00, '4-8 hours', 'fa-user-nurse', 1),
(3, 'Physical Therapy', 'Rehabilitation services at your doorstep', 1800.00, '1-2 hours', 'fa-walking', 1),
(4, '24/7 Doctor Support', 'Round-the-clock medical assistance', 3500.00, '24 hours', 'fa-user-md', 1),
(5, 'Companionship Care', 'Friendly companionship and basic assistance', 1500.00, '4-8 hours', 'fa-hands-helping', 1);

-- Create bookings table with foreign key reference
CREATE TABLE IF NOT EXISTS `bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(20) DEFAULT NULL,
  `client_name` varchar(100) NOT NULL,
  `client_email` varchar(100) NOT NULL,
  `client_phone` varchar(20) NOT NULL,
  `service_id` int(11) DEFAULT NULL, /* Explicitly nullable */
  `booking_date` date NOT NULL,
  `booking_time` time NOT NULL,
  `address` text NOT NULL,
  `special_requests` text DEFAULT NULL,
  `status` enum('pending','confirmed','completed','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `booking_date` (`booking_date`),
  KEY `reference` (`reference`),
  KEY `service_id` (`service_id`),
  CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create inquiries table
CREATE TABLE IF NOT EXISTS `inquiries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `status` enum('new','in_progress','resolved') NOT NULL DEFAULT 'new',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data into bookings table
INSERT INTO `bookings` (`client_name`, `client_email`, `client_phone`, `service_id`, `booking_date`, `booking_time`, `address`, `special_requests`, `status`) VALUES
('John Doe', '<EMAIL>', '+977 **********', 1, '2025-05-25', '10:00:00', '123 Main St, Kathmandu', 'Please bring blood pressure monitor', 'pending'),
('Jane Smith', '<EMAIL>', '+977 **********', 2, '2025-05-26', '14:30:00', '456 Park Ave, Lalitpur', 'Patient has mobility issues', 'confirmed'),
('Michael Johnson', '<EMAIL>', '+977 **********', 3, '2025-05-27', '09:15:00', '789 Oak Rd, Bhaktapur', 'Need assistance with medication', 'completed'),
('Sarah Williams', '<EMAIL>', '+977 **********', 1, '2025-05-28', '16:00:00', '321 Pine St, Kathmandu', 'Elderly patient needs gentle care', 'pending'),
('Robert Brown', '<EMAIL>', '+977 **********', 2, '2025-05-29', '11:30:00', '654 Maple Ave, Lalitpur', 'Patient has diabetes', 'cancelled');

-- Insert sample data into inquiries table
INSERT INTO `inquiries` (`name`, `email`, `phone`, `subject`, `message`, `status`) VALUES
('Rajesh Kumar', '<EMAIL>', '+977 **********', 'Question about home care services', 'I would like to know more about your home nursing services for my elderly mother. What are your rates and availability?', 'new'),
('Priya Sharma', '<EMAIL>', '+977 **********', 'Availability in Bhaktapur', 'Do you provide services in the Bhaktapur area? I need regular check-ups for my father who recently had surgery.', 'in_progress'),
('Amit Patel', '<EMAIL>', '+977 **********', 'Pricing inquiry', 'Could you please provide me with your pricing details for 24/7 care? I need someone to look after my grandmother.', 'resolved'),
('Sunita Thapa', '<EMAIL>', '+977 **********', 'Emergency services', 'Do you offer emergency medical services? What is the response time in case of an emergency?', 'new'),
('Binod Shrestha', '<EMAIL>', '+977 **********', 'Specialized care for stroke patient', 'My father recently had a stroke and needs specialized care. Do you have staff trained in stroke rehabilitation?', 'in_progress');

-- Create jobs table
CREATE TABLE IF NOT EXISTS `jobs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `requirements` text DEFAULT NULL,
  `responsibilities` text DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `job_type` enum('full_time','part_time','contract','internship','trainee') NOT NULL DEFAULT 'full_time',
  `experience_level` enum('entry','mid','senior','executive') DEFAULT 'entry',
  `salary_range` varchar(100) DEFAULT NULL,
  `application_deadline` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`),
  KEY `job_type` (`job_type`),
  KEY `application_deadline` (`application_deadline`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create job applications table
CREATE TABLE IF NOT EXISTS `job_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_id` int(11) NOT NULL,
  `applicant_name` varchar(100) NOT NULL,
  `applicant_email` varchar(100) NOT NULL,
  `applicant_phone` varchar(20) NOT NULL,
  `cover_letter` text DEFAULT NULL,
  `resume_filename` varchar(255) DEFAULT NULL,
  `resume_path` varchar(500) DEFAULT NULL,
  `status` enum('new','reviewed','shortlisted','interviewed','hired','rejected') NOT NULL DEFAULT 'new',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `job_id` (`job_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `job_applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample job postings
INSERT INTO `jobs` (`title`, `description`, `requirements`, `responsibilities`, `location`, `job_type`, `experience_level`, `salary_range`, `application_deadline`, `is_active`) VALUES
('Registered Nurse', 'We are looking for a compassionate and skilled Registered Nurse to join our home healthcare team. You will provide quality nursing care to patients in their homes, ensuring their comfort and well-being.', 'Bachelor''s degree in Nursing\nValid nursing license\nMinimum 2 years of experience in healthcare\nExcellent communication skills\nAbility to work independently\nCompassionate and patient-centered approach', 'Provide direct patient care in home settings\nAdminister medications and treatments\nMonitor patient vital signs and health status\nEducate patients and families about health conditions\nMaintain accurate patient records\nCoordinate with doctors and other healthcare professionals', 'Kathmandu', 'full_time', 'mid', 'Rs. 40,000 - 60,000', '2025-08-31', 1),
('Home Health Aide', 'Join our team as a Home Health Aide and make a difference in patients'' lives by providing essential care and support in their homes.', 'High school diploma or equivalent\nCertification as Home Health Aide\nBasic knowledge of healthcare procedures\nGood physical stamina\nReliable transportation\nCompassionate nature', 'Assist patients with daily living activities\nHelp with personal hygiene and grooming\nPrepare meals and assist with feeding\nProvide companionship and emotional support\nLight housekeeping related to patient care\nReport changes in patient condition', 'Kathmandu Valley', 'full_time', 'entry', 'Rs. 25,000 - 35,000', '2025-08-15', 1),
('Physical Therapist', 'We are seeking a licensed Physical Therapist to provide rehabilitation services to patients in their homes, helping them recover and improve their mobility.', 'Bachelor''s or Master''s degree in Physical Therapy\nValid PT license\nMinimum 1 year of experience\nKnowledge of home-based therapy techniques\nStrong interpersonal skills\nAbility to travel to patient homes', 'Conduct physical therapy assessments\nDevelop and implement treatment plans\nProvide therapeutic exercises and interventions\nEducate patients and families on exercises\nDocument patient progress\nCollaborate with healthcare team', 'Kathmandu, Lalitpur, Bhaktapur', 'full_time', 'mid', 'Rs. 50,000 - 70,000', '2025-09-15', 1),
('Medical Assistant Trainee', 'Great opportunity for recent graduates or students to gain hands-on experience in home healthcare while working alongside experienced medical professionals.', 'Currently enrolled in or recently graduated from medical assistant program\nBasic knowledge of medical terminology\nWillingness to learn and adapt\nGood communication skills\nReliable and punctual\nInterest in home healthcare', 'Shadow experienced healthcare professionals\nAssist with basic patient care tasks\nHelp with administrative duties\nLearn proper documentation procedures\nParticipate in training programs\nProvide support to healthcare team', 'Kathmandu', 'trainee', 'entry', 'Rs. 15,000 - 20,000', '2025-07-31', 1);
