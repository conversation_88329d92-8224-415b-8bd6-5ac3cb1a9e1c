// Validate date to ensure it's not in the past
function validateDate(dateInput) {
    const selectedDate = new Date(dateInput.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
        alert('Please select a future date');
        dateInput.value = '';
        return false;
    }
    return true;
}

// Initialize date restrictions and multi-step form
document.addEventListener('DOMContentLoaded', function() {
    console.log('Booking.js loaded successfully');
    
    // Check if required elements exist
    const bookingForm = document.getElementById('bookingForm');
    const dateInput = document.getElementById('preferredDate');
    
    if (!bookingForm) {
        console.error('Booking form not found!');
        return;
    }
    
    if (!dateInput) {
        console.error('Date input not found!');
        return;
    }
    
    console.log('Required elements found, proceeding with initialization');
    
    // Date initialization
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;

    dateInput.addEventListener('change', function() {
        validateDate(this);
    });

    // Initialize duration options with default values
    initializeDurationOptions();

    // Multi-step form initialization
    initMultiStepForm();
});

// Initialize duration options with default values
function initializeDurationOptions() {
    const durationSelect = document.getElementById('duration');

    // Clear existing options
    durationSelect.innerHTML = '<option value="">Select duration...</option>';

    // Add default duration options - only 12 hours and 24 hours
    const defaultDurations = [
        ['12-hours', '12 Hours'],
        ['24-hours', '24 Hours']
    ];

    defaultDurations.forEach(([value, text]) => {
        const option = new Option(text, value);
        durationSelect.add(option);
    });
}

// Multi-step form functionality
function initMultiStepForm() {
    const formSections = document.querySelectorAll('.form-section');
    const progressSteps = document.querySelectorAll('.progress-step');
    let currentSection = 0;

    // Next button click handler
    document.querySelectorAll('.btn-next').forEach(button => {
        button.addEventListener('click', function() {
            // Validate current section
            if (validateSection(currentSection)) {
                // Hide current section
                formSections[currentSection].classList.remove('active');
                // Mark current step as completed
                progressSteps[currentSection].classList.add('completed');
                // Show next section
                currentSection++;
                formSections[currentSection].classList.add('active');
                progressSteps[currentSection].classList.add('active');
                // Scroll to top of form
                document.querySelector('.booking-form').scrollIntoView({ behavior: 'smooth' });

                // Update summary if on last step
                if (currentSection === formSections.length - 1) {
                    updateSummary();
                }
            }
        });
    });

    // Previous button click handler
    document.querySelectorAll('.btn-prev').forEach(button => {
        button.addEventListener('click', function() {
            // Hide current section
            formSections[currentSection].classList.remove('active');
            progressSteps[currentSection].classList.remove('active');
            // Show previous section
            currentSection--;
            formSections[currentSection].classList.add('active');
            // Scroll to top of form
            document.querySelector('.booking-form').scrollIntoView({ behavior: 'smooth' });
        });
    });

    // Validate section
    function validateSection(sectionIndex) {
        const section = formSections[sectionIndex];
        const requiredInputs = section.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('error');

                // Add error message if not exists
                if (!input.nextElementSibling || !input.nextElementSibling.classList.contains('error-message')) {
                    const errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');

                    // Special message for duration
                    if (input.id === 'duration') {
                        errorMsg.textContent = 'Please select a duration. If no options appear, please select a service first.';
                    } else {
                        errorMsg.textContent = 'This field is required';
                    }

                    input.parentNode.insertBefore(errorMsg, input.nextSibling);
                }
            } else {
                input.classList.remove('error');
                if (input.nextElementSibling && input.nextElementSibling.classList.contains('error-message')) {
                    input.nextElementSibling.remove();
                }
            }
        });

        // Special validation for email
        if (sectionIndex === 0) {
            const emailInput = document.getElementById('email');
            if (emailInput.value && !validateEmail(emailInput.value)) {
                isValid = false;
                emailInput.classList.add('error');

                if (!emailInput.nextElementSibling || !emailInput.nextElementSibling.classList.contains('error-message')) {
                    const errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');
                    errorMsg.textContent = 'Please enter a valid email address';
                    emailInput.parentNode.insertBefore(errorMsg, emailInput.nextSibling);
                }
            }

            // Phone validation
            const phoneInput = document.getElementById('phone');
            if (phoneInput.value && !validatePhone(phoneInput.value)) {
                isValid = false;
                phoneInput.classList.add('error');

                if (!phoneInput.nextElementSibling || !phoneInput.nextElementSibling.classList.contains('error-message')) {
                    const errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');
                    errorMsg.textContent = 'Please enter a valid phone number';
                    phoneInput.parentNode.insertBefore(errorMsg, phoneInput.nextSibling);
                }
            }
        }

        if (!isValid) {
            // Scroll to first error
            const firstError = section.querySelector('.error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }

        return isValid;
    }

    // Email validation
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Phone validation
    function validatePhone(phone) {
        const phoneRegex = /^\+?[\d\s-]{10,}$/;
        return phoneRegex.test(phone);
    }

    // Update booking summary
    function updateSummary() {
        document.getElementById('summaryName').textContent = `${document.getElementById('firstName').value} ${document.getElementById('lastName').value}`;
        document.getElementById('summaryEmail').textContent = document.getElementById('email').value;
        document.getElementById('summaryPhone').textContent = document.getElementById('phone').value;

        // Service
        const serviceSelect = document.getElementById('service');
        document.getElementById('summaryService').textContent = serviceSelect.options[serviceSelect.selectedIndex].text;

        // Date & Time
        const timeSelect = document.getElementById('preferredTime');
        const dateValue = document.getElementById('preferredDate').value;
        const formattedDate = dateValue ? new Date(dateValue).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) : '';
        document.getElementById('summaryDateTime').textContent = `${formattedDate}, ${timeSelect.options[timeSelect.selectedIndex].text}`;

        // Duration
        const durationSelect = document.getElementById('duration');
        document.getElementById('summaryDuration').textContent = durationSelect.options[durationSelect.selectedIndex].text;
    }
}

// Handle form submission - ensure this runs after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const bookingForm = document.getElementById('bookingForm');
    
    if (!bookingForm) {
        console.error('Cannot attach submit handler - booking form not found!');
        return;
    }
    
    console.log('Attaching submit event handler to booking form');
    
    bookingForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        console.log('Form submission intercepted successfully');

    // Form validation
    const requiredFields = [
        'firstName', 'lastName', 'email', 'phone',
        'service', 'careType', 'preferredDate', 'preferredTime',
        'duration', 'emergencyName', 'emergencyPhone', 'relationship'
    ];

    let isValid = true;
    const formData = new FormData(this);
    
    console.log('Form data collected:');
    for (let [key, value] of formData.entries()) {
        console.log(key + ':', value);
    }

    // Check required fields
    requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (!formData.get(field)) {
            isValid = false;
            input.classList.add('error');
        } else {
            input.classList.remove('error');
        }
    });

    // Check terms and conditions checkbox
    const termsCheckbox = document.getElementById('terms');
    if (!termsCheckbox.checked) {
        isValid = false;
        termsCheckbox.classList.add('error');
        alert('Please accept the Terms and Conditions to proceed.');
        return;
    } else {
        termsCheckbox.classList.remove('error');
    }

    if (!isValid) {
        // Check if duration is one of the missing fields
        if (!formData.get('duration')) {
            alert('Please fill in all required fields. Make sure to select a duration - if no options appear, please select a service first.');
        } else {
            alert('Please fill in all required fields');
        }
        return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.get('email'))) {
        alert('Please enter a valid email address');
        document.getElementById('email').classList.add('error');
        return;
    }

    // Phone validation
    const phoneRegex = /^\+?[\d\s-]{10,}$/;
    if (!phoneRegex.test(formData.get('phone'))) {
        alert('Please enter a valid phone number');
        document.getElementById('phone').classList.add('error');
        return;
    }

    // Date validation
    const selectedDate = new Date(formData.get('preferredDate'));
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
        alert('Please select a future date');
        document.getElementById('preferredDate').classList.add('error');
        return;
    }

    // Submit form via AJAX
    const submitButton = this.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    submitButton.innerHTML = 'Processing...';    // Get the current base URL to ensure correct path
    const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
    const processUrl = baseUrl + 'process_booking.php';
    
    console.log('Submitting to URL:', processUrl);
    
    fetch(processUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers.get('content-type'));

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Get response text first to debug
        return response.text().then(text => {
            console.log('Raw response:', text);

            // Check if response looks like JSON
            if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error('Invalid JSON response: ' + e.message);
                }
            } else {
                throw new Error('Server returned HTML instead of JSON. Response: ' + text.substring(0, 200));
            }
        });
    })
    .then(data => {
        submitButton.disabled = false;
        submitButton.innerHTML = 'Schedule Appointment';

        console.log('Booking response:', data); // Debug logging

        if (data.success) {
            // Update reference number and show success modal
            document.getElementById('bookingReference').textContent = data.reference;
            document.getElementById('bookingSuccessModal').style.display = 'block';

            // Reset form
            this.reset();

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            // Log success for debugging
            console.log('Booking successful:', data.reference);
        } else {
            // Show the error message from server
            const errorMessage = data.message || 'An error occurred. Please try again.';
            alert(errorMessage);
            console.error('Booking failed:', data);
        }
    })
    .catch(error => {
        submitButton.disabled = false;
        submitButton.innerHTML = 'Schedule Appointment';

        console.error('Booking error details:', error);

        // More specific error messages
        let errorMessage = 'An error occurred. Please try again later.';
        if (error.message.includes('HTTP error')) {
            errorMessage = 'Server error. Please contact support if the problem persists.';
        } else if (error.message.includes('JSON')) {
            errorMessage = 'Invalid server response. Please contact support.';
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Network error. Please check your internet connection and try again.';
        }        alert(errorMessage);
    });
});

// Service selection handler
document.addEventListener('DOMContentLoaded', function() {
    const serviceSelect = document.getElementById('service');
    if (serviceSelect) {
        serviceSelect.addEventListener('change', function() {
    const service = this.value;
    const durationSelect = document.getElementById('duration');

    // Save the currently selected value if any
    const currentValue = durationSelect.value;

    // Reset duration options
    durationSelect.innerHTML = '<option value="">Select duration...</option>';

    // Add duration options - only 12 hours and 24 hours for all services
    const durations = [
        ['12-hours', '12 Hours'],
        ['24-hours', '24 Hours']
    ];

    durations.forEach(([value, text]) => {
        const option = new Option(text, value);
        durationSelect.add(option);
    });

    // Try to restore the previously selected value if it exists in the new options
    if (currentValue) {
        // Check if the option exists in the new list
        const optionExists = Array.from(durationSelect.options).some(option => option.value === currentValue);
        if (optionExists) {
            durationSelect.value = currentValue;
        }
    }
});

// Care type change handler
document.getElementById('careType').addEventListener('change', function() {
    const careType = this.value;
    const durationSelect = document.getElementById('duration');

    // For recurring care, we might want to handle duration differently
    if (careType === 'recurring') {
        // Instead of disabling, we'll just add a note
        const durationLabel = durationSelect.previousElementSibling;
        if (durationLabel && durationLabel.tagName === 'LABEL') {
            if (!durationLabel.querySelector('.note')) {
                const note = document.createElement('span');
                note.classList.add('note');
                note.textContent = ' (per visit)';
                note.style.fontSize = '0.8em';
                note.style.color = '#666';
                durationLabel.appendChild(note);
            }
        }
    } else {
        // Remove the note if it exists
        const durationLabel = durationSelect.previousElementSibling;
        if (durationLabel && durationLabel.tagName === 'LABEL') {
            const note = durationLabel.querySelector('.note');
            if (note) {
                note.remove();
            }
        }
    }
});

// Error highlighting for inputs
document.querySelectorAll('input, select, textarea').forEach(input => {
    input.addEventListener('focus', function() {
        this.classList.remove('error');
    });
});

// Modal close handlers
document.querySelector('.close').addEventListener('click', function() {
    document.getElementById('bookingSuccessModal').style.display = 'none';
});

window.addEventListener('click', function(e) {
    const modal = document.getElementById('bookingSuccessModal');
    if (e.target === modal) {
        modal.style.display = 'none';
    }
});

// Character counter for textareas
document.querySelectorAll('textarea').forEach(textarea => {
    textarea.addEventListener('input', function() {
        const maxLength = this.getAttribute('maxlength') || 500;
        const remaining = maxLength - this.value.length;
        const counter = this.nextElementSibling;

        if (!counter || !counter.classList.contains('char-counter')) {
            const counterElem = document.createElement('div');
            counterElem.classList.add('char-counter');
            counterElem.textContent = `${remaining} characters remaining`;
            this.parentNode.insertBefore(counterElem, this.nextSibling);
        } else {
            counter.textContent = `${remaining} characters remaining`;
        }
    });
});
