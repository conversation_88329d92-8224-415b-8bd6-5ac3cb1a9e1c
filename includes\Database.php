<?php
// Include the configuration file if it hasn't been included yet
if (!defined('DB_HOST')) {
    require_once __DIR__ . '/config.php';
}

class Database {
    private $conn = null;
    private static $instance = null;

    private function __construct() {
        try {
            $this->conn = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
                DB_USER,
                DB_PASS
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->conn;
    }

    public function query($sql, $params = []) {
        try {
            // Fix for LIMIT ? issue - convert numeric parameters to integers
            if (is_array($params) && !empty($params)) {
                foreach ($params as $key => $value) {
                    // If parameter is used in a LIMIT clause, ensure it's an integer
                    if (is_numeric($value) && strpos($sql, 'LIMIT') !== false) {
                        $params[$key] = (int)$value;
                    }
                }
            }

            $stmt = $this->conn->prepare($sql);

            // Fix for potential issue with numeric array keys in parameters
            if (is_array($params) && !empty($params)) {
                if (array_keys($params) === range(0, count($params) - 1)) {
                    // Sequential array - use positional parameters
                    $stmt->execute($params);
                } else {
                    // Associative array - bind each parameter separately
                    foreach ($params as $key => $value) {
                        // If the key is numeric, convert it to a positional parameter
                        if (is_numeric($key)) {
                            $stmt->bindValue($key + 1, $value);
                        } else {
                            $stmt->bindValue(':' . $key, $value);
                        }
                    }
                    $stmt->execute();
                }
            } else {
                $stmt->execute();
            }

            return $stmt;
        } catch(PDOException $e) {
            // Log the error with specifics
            error_log("Database query error: " . $e->getMessage());
            error_log("SQL: " . $sql);

            // Throw the exception to be handled by the calling code
            throw $e;
        }
    }

    public function select($sql, $params = []) {
        try {
            return $this->query($sql, $params)->fetchAll();
        } catch(PDOException $e) {
            // Use a more graceful error handling approach
            error_log("SELECT query error: " . $e->getMessage());
            error_log("SQL: " . $sql);
            // Return empty array instead of crashing
            return [];
        }
    }

    public function selectOne($sql, $params = []) {
        try {
            $result = $this->query($sql, $params)->fetch();
            return $result !== false ? $result : null;
        } catch(PDOException $e) {
            error_log("SELECT ONE query error: " . $e->getMessage());
            error_log("SQL: " . $sql);
            return null;
        }
    }    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = array_map(function($field) { return ":$field"; }, $fields);

        $sql = "INSERT INTO $table (" . implode(", ", $fields) . ")
                VALUES (" . implode(", ", $placeholders) . ")";

        try {
            error_log("INSERT SQL: " . $sql);
            error_log("INSERT Data: " . print_r($data, true));
            
            $this->query($sql, $data);
            $lastId = $this->conn->lastInsertId();
            
            error_log("INSERT successful, last ID: " . $lastId);
            return $lastId;
        } catch(PDOException $e) {
            error_log("INSERT query error: " . $e->getMessage());
            error_log("Table: " . $table);
            error_log("Data: " . print_r($data, true));
            error_log("SQL: " . $sql);
            throw $e; // Re-throw the exception so it can be caught by the calling code
        }
    }

    public function update($table, $data, $where, $whereParams = []) {
        try {
            // Special handling for blog_posts table to ensure proper updates
            if ($table === 'blog_posts' && strpos($where, 'id = ?') !== false && count($whereParams) === 1) {
                $postId = $whereParams[0];

                // Build the SQL query with direct parameter binding for blog posts
                $setParts = [];
                $params = [];

                foreach ($data as $field => $value) {
                    $setParts[] = "$field = ?";
                    $params[] = $value;
                }

                // Add the ID parameter
                $params[] = $postId;

                $sql = "UPDATE blog_posts SET " . implode(", ", $setParts) . " WHERE id = ?";

                // Debug information
                error_log("Blog Update SQL: " . $sql);
                error_log("Blog Update Params: " . print_r($params, true));

                $stmt = $this->conn->prepare($sql);
                $result = $stmt->execute($params);

                if (!$result) {
                    error_log("Blog update failed. Error info: " . print_r($stmt->errorInfo(), true));
                    return false;
                }

                return true;
            }

            // For settings table, use a simpler approach to avoid parameter binding issues
            if ($table === 'settings' && count($data) === 1 && isset($whereParams[0]) && isset($whereParams[1])) {
                // Get the field and value
                $field = array_keys($data)[0];
                $value = $data[$field];

                // Prepare the SQL with direct parameter binding
                $sql = "UPDATE settings SET setting_value = ? WHERE section = ? AND setting_key = ?";
                $params = [$value, $whereParams[0], $whereParams[1]];

                $stmt = $this->conn->prepare($sql);
                $stmt->execute($params);

                return true;
            }

            // For pricing_plans table, use direct parameter binding to avoid issues
            if ($table === 'pricing_plans' && isset($whereParams[0])) {
                $id = $whereParams[0];

                // Build the SQL query with direct parameter binding
                $setParts = [];
                $params = [];

                foreach ($data as $field => $value) {
                    $setParts[] = "$field = ?";
                    $params[] = $value;
                }

                // Add the ID parameter
                $params[] = $id;

                $sql = "UPDATE $table SET " . implode(", ", $setParts) . " WHERE id = ?";

                // Debug information
                error_log("Direct SQL: " . $sql);
                error_log("Direct Params: " . print_r($params, true));

                $stmt = $this->conn->prepare($sql);
                $stmt->execute($params);

                return true;
            }

            // For services table, use direct parameter binding like other tables
            if ($table === 'services' && isset($whereParams[0])) {
                $id = $whereParams[0];

                // Build the SQL query with direct parameter binding
                $setParts = [];
                $params = [];

                foreach ($data as $field => $value) {
                    $setParts[] = "$field = ?";
                    $params[] = $value;
                }

                // Add the ID parameter
                $params[] = $id;

                $sql = "UPDATE $table SET " . implode(", ", $setParts) . " WHERE id = ?";

                // Debug information
                error_log("Services Update SQL: " . $sql);
                error_log("Services Update Params: " . print_r($params, true));

                $stmt = $this->conn->prepare($sql);
                $stmt->execute($params);

                return true;
            }

            // If we get here, use the standard approach
            $setParts = [];
            $params = [];

            // Handle special case for expressions like "views + 1"
            foreach ($data as $field => $value) {
                if (is_array($value) && isset($value['expr'])) {
                    $setParts[] = "$field = " . $value['expr'];
                } else {
                    $setParts[] = "$field = :update_$field";
                    $params["update_$field"] = $value;
                }
            }

            // If there are no fields to update, return true (we might have handled content separately)
            if (empty($setParts)) {
                return true;
            }

            // Convert positional where parameters to named parameters
            $whereConditions = $where;
            foreach ($whereParams as $index => $value) {
                $paramName = "where_param_$index";
                $whereConditions = preg_replace('/\?/', ":$paramName", $whereConditions, 1);
                $params[$paramName] = $value;
            }

            $sql = "UPDATE $table SET " . implode(", ", $setParts) . " WHERE $whereConditions";

            // Debug information
            error_log("SQL: " . $sql);
            error_log("Params: " . print_r($params, true));

            $stmt = $this->query($sql, $params);

            // Even if no rows were affected, consider it a success if the query executed
            // This handles cases where the data didn't actually change
            return true;
        } catch(PDOException $e) {
            error_log("UPDATE query error: " . $e->getMessage());
            error_log("Table: " . $table);
            return false;
        }
    }

    public function delete($table, $where, $params = []) {
        try {
            $sql = "DELETE FROM $table WHERE $where";
            $stmt = $this->query($sql, $params);
            $rowCount = $stmt->rowCount();

            // Return true if at least one row was affected or if we're deleting from a junction table
            // (for junction tables, it's normal to have 0 rows affected if there are no relations)
            return $rowCount > 0 || $table === 'post_categories' || $table === 'post_tags' || $table === 'blog_comments';
        } catch(PDOException $e) {
            error_log("DELETE query error: " . $e->getMessage());
            error_log("Table: " . $table);
            error_log("Where: " . $where);
            error_log("Params: " . print_r($params, true));
            return false;
        }
    }
}