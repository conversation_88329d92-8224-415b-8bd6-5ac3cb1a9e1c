<?php
session_start();
require_once 'includes/auth.php';
require_admin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Include Database class
require_once '../includes/Database.php';
$db = Database::getInstance();

// Get action from URL
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$commentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Message handling
$message = '';
$messageType = '';

// Handle comment actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $postAction = $_POST['action'];

        if ($postAction === 'approve' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            try {
                $db->update('blog_comments', ['status' => 'approved'], 'id = ?', [$id]);
                $message = 'Comment approved successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error: ' . $e->getMessage();
                $messageType = 'error';
            }
        } elseif ($postAction === 'reject' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            try {
                $db->update('blog_comments', ['status' => 'rejected'], 'id = ?', [$id]);
                $message = 'Comment rejected successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error: ' . $e->getMessage();
                $messageType = 'error';
            }
        } elseif ($postAction === 'delete' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            try {
                $db->delete('blog_comments', 'id = ?', [$id]);
                $message = 'Comment deleted successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get comments
$comments = [];
try {
    $comments = $db->select(
        "SELECT c.*, p.title as post_title
         FROM blog_comments c
         JOIN blog_posts p ON c.post_id = p.id
         ORDER BY c.created_at DESC"
    );
} catch (Exception $e) {
    $message = 'Error: ' . $e->getMessage();
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Blog Comments - Doctors At Door Step</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--dark-color);
            color: var(--white);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        /* Alert Messages */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        /* Table Styles */
        .comments-table {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        th {
            background-color: var(--light-color);
            font-weight: 600;
        }

        tr:hover {
            background-color: rgba(44, 123, 229, 0.05);
        }

        .comment-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #FEF3C7;
            color: #D97706;
        }

        .status-approved {
            background-color: #D1FAE5;
            color: #059669;
        }

        .status-rejected {
            background-color: #FEE2E2;
            color: #DC2626;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-action {
            padding: 5px 10px;
            border-radius: 5px;
            color: var(--white);
            text-decoration: none;
            font-size: 12px;
            cursor: pointer;
            border: none;
        }

        .btn-approve {
            background-color: var(--success-color);
        }

        .btn-reject {
            background-color: var(--error-color);
        }

        .btn-delete {
            background-color: #6B7280;
        }

        .comment-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Responsive */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .toggle-sidebar {
                display: block;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">Manage Blog Comments</h1>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Comments List -->
        <div class="comments-table">
            <table>
                <thead>
                    <tr>
                        <th>Author</th>
                        <th>Comment</th>
                        <th>Post</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($comments)): ?>
                        <tr>
                            <td colspan="6" style="text-align: center;">No comments found.</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($comments as $comment): ?>
                            <tr>
                                <td>
                                    <?php echo htmlspecialchars($comment['author_name']); ?><br>
                                    <small><?php echo htmlspecialchars($comment['author_email']); ?></small>
                                </td>
                                <td class="comment-content"><?php echo htmlspecialchars($comment['content']); ?></td>
                                <td><?php echo htmlspecialchars($comment['post_title']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($comment['created_at'])); ?></td>
                                <td>
                                    <span class="comment-status status-<?php echo $comment['status']; ?>">
                                        <?php echo ucfirst($comment['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <?php if ($comment['status'] !== 'approved'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="approve">
                                                <input type="hidden" name="id" value="<?php echo $comment['id']; ?>">
                                                <button type="submit" class="btn-action btn-approve">Approve</button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if ($comment['status'] !== 'rejected'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="reject">
                                                <input type="hidden" name="id" value="<?php echo $comment['id']; ?>">
                                                <button type="submit" class="btn-action btn-reject">Reject</button>
                                            </form>
                                        <?php endif; ?>

                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $comment['id']; ?>">
                                            <button type="submit" class="btn-action btn-delete">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>