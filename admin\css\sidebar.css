/* Admin Sidebar Styles */
:root {
    --primary-color: #2C7BE5;
    --secondary-color: #6B7A99;
    --dark-color: #1A2B3C;
    --light-color: #F8FAFC;
    --white: #FFFFFF;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --sidebar-width: 250px;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--dark-color);
    color: var(--white);
    padding: 20px 0;
    z-index: 1000;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-header h1 {
    font-size: 20px;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.sidebar-header p {
    font-size: 14px;
    opacity: 0.8;
}

.sidebar-menu {
    padding: 20px 0;
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 200px); /* Ensure there's enough space for all menu items */
}

.menu-item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s;
}

.menu-item:hover, .menu-item.active {
    background-color: rgba(44, 123, 229, 0.2);
    color: var(--primary-color);
}

.menu-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
    background-color: var(--dark-color);
}

.logout-btn {
    display: block;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    text-align: center;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Responsive */
@media (max-width: 991px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }
}
