<?php
// Prevent direct access
if (!defined('ALLOWED_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access not allowed');
}

require_once __DIR__ . '/Database.php';

class Partners {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }    /**
     * Get all partners
     */
    public function getAllPartners($includeInactive = false) {
        try {
            $sql = "SELECT * FROM partners";
            if (!$includeInactive) {
                $sql .= " WHERE status = 'active'";
            }
            $sql .= " ORDER BY display_order ASC, created_at ASC";
            
            $result = $this->db->select($sql);
            return $result;
        } catch (Exception $e) {
            error_log("Error fetching partners: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get active partners for public display
     */
    public function getActivePartners() {
        return $this->getAllPartners(false);
    }    /**
     * Get partner by ID
     */
    public function getPartnerById($id) {
        try {
            $sql = "SELECT * FROM partners WHERE id = ?";
            $result = $this->db->selectOne($sql, [$id]);
            return $result;
        } catch (Exception $e) {
            error_log("Error fetching partner by ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Add new partner
     */
    public function addPartner($name, $logoPath, $websiteUrl = null, $description = null, $displayOrder = 0) {
        try {
            $sql = "INSERT INTO partners (name, logo, website_url, description, display_order, status)
                    VALUES (?, ?, ?, ?, ?, 'active')";

            $params = [$name, $logoPath, $websiteUrl, $description, $displayOrder];
            return $this->db->query($sql, $params);
        } catch (Exception $e) {
            error_log("Error adding partner: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update partner
     */
    public function updatePartner($id, $name, $logoPath, $websiteUrl = null, $description = null, $displayOrder = 0, $status = 'active') {
        try {
            $sql = "UPDATE partners SET 
                    name = ?, 
                    logo = ?, 
                    website_url = ?, 
                    description = ?, 
                    display_order = ?, 
                    status = ?,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?";
            
            $params = [$name, $logoPath, $websiteUrl, $description, $displayOrder, $status, $id];
            return $this->db->query($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating partner: " . $e->getMessage());
            return false;
        }
    }    /**
     * Delete partner
     */
    public function deletePartner($id) {
        try {
            // First get the partner to delete the logo file
            $partner = $this->getPartnerById($id);
            if ($partner && file_exists($partner['logo'])) {
                unlink($partner['logo']);
            }

            $sql = "DELETE FROM partners WHERE id = ?";
            return $this->db->query($sql, [$id]);
        } catch (Exception $e) {
            error_log("Error deleting partner: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Toggle partner active status
     */
    public function togglePartnerStatus($id) {
        try {
            $sql = "UPDATE partners SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            return $this->db->query($sql, [$id]);
        } catch (Exception $e) {
            error_log("Error toggling partner status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update display order for partners
     */
    public function updateDisplayOrder($partnerOrders) {
        try {
            $this->db->getConnection()->beginTransaction();
            
            foreach ($partnerOrders as $id => $order) {
                $sql = "UPDATE partners SET display_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
                $this->db->query($sql, [$order, $id]);
            }
            
            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollback();
            error_log("Error updating display order: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle logo upload
     */
    public function handleLogoUpload($file) {
        try {
            // Use relative path from the project root instead of DOCUMENT_ROOT
            $uploadDir = dirname(__DIR__) . '/uploads/partners/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Validate file
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.');
            }

            if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
                throw new Exception('File size too large. Maximum 5MB allowed.');
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'partner_' . time() . '_' . uniqid() . '.' . $extension;
            $targetPath = $uploadDir . $filename;
            $relativePath = 'uploads/partners/' . $filename;

            if (move_uploaded_file($file['tmp_name'], $targetPath)) {
                return $relativePath;
            } else {
                throw new Exception('Failed to upload file.');
            }
        } catch (Exception $e) {
            error_log("Error uploading logo: " . $e->getMessage());
            throw $e;
        }
    }    /**
     * Get partner statistics
     */
    public function getStats() {
        try {
            $totalSql = "SELECT COUNT(*) as total FROM partners";
            $activeSql = "SELECT COUNT(*) as active FROM partners WHERE status = 'active'";
            
            $totalResult = $this->db->selectOne($totalSql);
            $activeResult = $this->db->selectOne($activeSql);
            
            $total = $totalResult['total'] ?? 0;
            $active = $activeResult['active'] ?? 0;
            
            return [
                'total' => $total,
                'active' => $active,
                'inactive' => $total - $active
            ];
        } catch (Exception $e) {
            error_log("Error getting partner stats: " . $e->getMessage());
            return ['total' => 0, 'active' => 0, 'inactive' => 0];
        }
    }
}
?>