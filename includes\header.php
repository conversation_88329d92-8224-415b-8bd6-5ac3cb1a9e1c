<?php
if (!defined('ALLOWED_ACCESS')) {
    define('ALLOWED_ACCESS', true);
}
require_once __DIR__ . '/config.php';

// Include Settings class
require_once __DIR__ . '/Settings.php';
$settingsHandler = new Settings();

// Get site settings
$siteSettings = [
    'site_name' => $settingsHandler->get('site_name', 'Doctors At Door Step'),
    'site_tagline' => $settingsHandler->get('site_tagline', 'Healthcare at your doorstep'),
    'site_description' => $settingsHandler->get('site_description', ''),
    'logo' => $settingsHandler->get('logo', 'images/logo.png'),
    'favicon' => $settingsHandler->get('favicon', 'images/favicon.ico')
];

// Check if maintenance mode is enabled
$maintenanceMode = $settingsHandler->get('maintenance_mode', '0') === '1';
if ($maintenanceMode && !isset($_SESSION['admin_logged_in'])) {
    // Only display maintenance page to non-admin users
    include 'maintenance.php';
    exit;
}

// Set debug mode based on settings
$debugMode = $settingsHandler->get('debug_mode', '0') === '1';
if ($debugMode) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// Set timezone
$timezone = $settingsHandler->get('timezone', 'Asia/Kathmandu');
date_default_timezone_set($timezone);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <?php
    // Dynamic title and description for blog posts and other pages
    $page_title = isset($GLOBALS['page_title']) ? $GLOBALS['page_title'] : $siteSettings['site_name'] . ' - ' . $siteSettings['site_tagline'];
    $page_description = isset($GLOBALS['page_description']) ? $GLOBALS['page_description'] : $siteSettings['site_description'];
    ?>
    
    <title><?php echo htmlspecialchars($page_title); ?></title>

    <?php if (!empty($page_description)): ?>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <?php endif; ?>

    <!-- Open Graph Meta Tags for Social Media -->
    <?php if (isset($GLOBALS['og_title'])): ?>
    <meta property="og:title" content="<?php echo htmlspecialchars($GLOBALS['og_title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($GLOBALS['og_description']); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($GLOBALS['og_url']); ?>">
    <meta property="og:type" content="<?php echo htmlspecialchars($GLOBALS['og_type']); ?>">
    <meta property="og:site_name" content="<?php echo htmlspecialchars($siteSettings['site_name']); ?>">
    
    <?php if (!empty($GLOBALS['og_image'])): ?>
    <meta property="og:image" content="<?php echo htmlspecialchars($GLOBALS['og_image']); ?>">
    <meta property="og:image:alt" content="<?php echo htmlspecialchars($GLOBALS['og_title']); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <?php endif; ?>
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($GLOBALS['og_title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($GLOBALS['og_description']); ?>">
    <?php if (!empty($GLOBALS['og_image'])): ?>
    <meta name="twitter:image" content="<?php echo htmlspecialchars($GLOBALS['og_image']); ?>">
    <?php endif; ?>
    
    <?php else: ?>
    <!-- Default Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($siteSettings['site_name']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($siteSettings['site_description']); ?>">
    <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?php echo htmlspecialchars($siteSettings['site_name']); ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($siteSettings['site_name']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($siteSettings['site_description']); ?>">
    <?php endif; ?>    <!-- Favicon -->
    <?php if (!empty($siteSettings['favicon'])): ?>
    <link rel="icon" href="<?php echo htmlspecialchars($siteSettings['favicon']); ?>" type="image/x-icon">
    <?php endif; ?>
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    
    <?php if (isset($GLOBALS['og_type']) && $GLOBALS['og_type'] === 'article'): ?>
    <!-- Structured Data for Article -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "<?php echo htmlspecialchars($GLOBALS['og_title']); ?>",
        "description": "<?php echo htmlspecialchars($GLOBALS['og_description']); ?>",
        "url": "<?php echo htmlspecialchars($GLOBALS['og_url']); ?>",
        <?php if (!empty($GLOBALS['og_image'])): ?>
        "image": "<?php echo htmlspecialchars($GLOBALS['og_image']); ?>",
        <?php endif; ?>
        "publisher": {
            "@type": "Organization",
            "name": "<?php echo htmlspecialchars($siteSettings['site_name']); ?>"
        }
    }
    </script>
    <?php endif; ?>

    <!-- Fonts & Styles -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Swiper JS for Testimonials -->
    <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css">    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/placeholder-images.css">
    <link rel="stylesheet" href="css/partners.css">

    <?php
    // Get the current page filename
    $current_page = basename($_SERVER['PHP_SELF']);

    // Include additional CSS files based on current page
    if ($current_page === 'service-details.php') {
        echo '<link rel="stylesheet" href="css/service-details.css">';
    }
    ?>
</head>
<body>
    <!-- Sticky Header -->
    <header class="header" id="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">
                <?php if (!empty($siteSettings['logo'])): ?>
                <img src="<?php echo htmlspecialchars($siteSettings['logo']); ?>" alt="<?php echo htmlspecialchars($siteSettings['site_name']); ?>" width="90px" height="80px">
                <?php else: ?>
                <h1><?php echo htmlspecialchars($siteSettings['site_name']); ?></h1>
                <?php endif; ?>
                </a>

                <div class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php" class="nav-link <?php echo $current_page === 'index.php' ? 'active' : ''; ?>">Home</a></li>
                    <li><a href="about.php" class="nav-link <?php echo $current_page === 'about.php' ? 'active' : ''; ?>">About Us</a></li>
                    <li><a href="services.php" class="nav-link <?php echo $current_page === 'services.php' || $current_page === 'service-details.php' ? 'active' : ''; ?>">Services</a></li>
                    <li><a href="faq.php" class="nav-link <?php echo $current_page === 'faq.php' ? 'active' : ''; ?>">FAQ</a></li>
                    <li><a href="blog.php" class="nav-link <?php echo $current_page === 'blog.php' ? 'active' : ''; ?>">Blog</a></li>
                    <li><a href="contact.php" class="nav-link <?php echo $current_page === 'contact.php' ? 'active' : ''; ?>">Contact</a></li>
                    <li><a href="booking.php" class="nav-link <?php echo $current_page === 'booking.php' ; ?> cta-button">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>