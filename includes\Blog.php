<?php
require_once 'Database.php';
class Blog {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();

        // Check if blog tables exist and create them if they don't
        $this->ensureBlogTablesExist();
    }

    /**
     * Check if blog tables exist and create them if they don't
     */
    private function ensureBlogTablesExist() {
        try {
            // Check if blog_posts table exists
            $result = $this->db->selectOne("SHOW TABLES LIKE 'blog_posts'");
            if (!$result) {
                error_log("Creating blog_posts table");
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS blog_posts (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        slug VARCHAR(255) UNIQUE NOT NULL,
                        excerpt TEXT,
                        content LONGTEXT,
                        featured_image VARCHAR(255),
                        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                        views INT DEFAULT 0,
                        author_id INT,
                        published_at DATETIME,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL
                    )
                ");
            }

            // Check if blog_categories table exists
            $result = $this->db->selectOne("SHOW TABLES LIKE 'blog_categories'");
            if (!$result) {
                error_log("Creating blog_categories table");
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS blog_categories (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        slug VARCHAR(100) UNIQUE NOT NULL,
                        description TEXT,
                        display_order INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
            }

            // Check if blog_tags table exists
            $result = $this->db->selectOne("SHOW TABLES LIKE 'blog_tags'");
            if (!$result) {
                error_log("Creating blog_tags table");
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS blog_tags (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        slug VARCHAR(100) UNIQUE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
            }

            // Check if post_categories table exists
            $result = $this->db->selectOne("SHOW TABLES LIKE 'post_categories'");
            if (!$result) {
                error_log("Creating post_categories table");
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS post_categories (
                        post_id INT,
                        category_id INT,
                        PRIMARY KEY (post_id, category_id),
                        FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
                        FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE CASCADE
                    )
                ");
            }

            // Check if post_tags table exists
            $result = $this->db->selectOne("SHOW TABLES LIKE 'post_tags'");
            if (!$result) {
                error_log("Creating post_tags table");
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS post_tags (
                        post_id INT,
                        tag_id INT,
                        PRIMARY KEY (post_id, tag_id),
                        FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
                        FOREIGN KEY (tag_id) REFERENCES blog_tags(id) ON DELETE CASCADE
                    )
                ");
            }

            // Check if blog_comments table exists
            $result = $this->db->selectOne("SHOW TABLES LIKE 'blog_comments'");
            if (!$result) {
                error_log("Creating blog_comments table");
                $this->db->query("
                    CREATE TABLE IF NOT EXISTS blog_comments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        post_id INT,
                        parent_id INT,
                        author_name VARCHAR(100) NOT NULL,
                        author_email VARCHAR(100) NOT NULL,
                        content TEXT NOT NULL,
                        status ENUM('pending', 'approved', 'spam', 'trash') DEFAULT 'pending',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
                        FOREIGN KEY (parent_id) REFERENCES blog_comments(id) ON DELETE CASCADE
                    )
                ");
            }
        } catch (Exception $e) {
            error_log("Error ensuring blog tables exist: " . $e->getMessage());
        }
    }

    /**
     * Get published blog posts with pagination
     */
    public function getPosts($page = 1, $per_page = 10) {
        $offset = ($page - 1) * $per_page;

        // Cast to integers to ensure they're not treated as strings
        $per_page = (int)$per_page;
        $offset = (int)$offset;

        return $this->db->select(
            "SELECT * FROM blog_posts
            WHERE status = 'published' AND published_at <= NOW()
            ORDER BY published_at DESC
            LIMIT $per_page OFFSET $offset"
        );
    }
    /**
     * Count total published posts
     */
    public function countPosts() {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM blog_posts
            WHERE status = 'published' AND published_at <= NOW()"
        );

        return $result ? $result['count'] : 0;
    }

    /**
     * Get a single post by slug
     */
    public function getPostBySlug($slug) {
        return $this->db->selectOne(
            "SELECT * FROM blog_posts WHERE slug = ? AND status = 'published' AND published_at <= NOW()",
            [$slug]
        );
    }

    /**
     * Get a single post by slug with fixed image paths for display
     */
    public function getPostBySlugForDisplay($slug) {
        $post = $this->getPostBySlug($slug);
        if ($post) {
            $posts = $this->fixImagePaths([$post]);
            return $posts[0];
        }
        return $post;
    }

    /**
     * Get a single post by ID
     */
    public function getPostById($id) {
        return $this->db->selectOne(
            "SELECT * FROM blog_posts WHERE id = ? AND status = 'published' AND published_at <= NOW()",
            [$id]
        );
    }

    /**
     * Get recent published posts
     */    public function getRecentPosts($limit = 5) {
        $limit = (int)$limit; // Ensure it's an integer
        return $this->db->select(
            "SELECT * FROM blog_posts
            WHERE status = 'published' AND published_at <= NOW()
            ORDER BY published_at DESC
            LIMIT $limit"
        );
    }

    /**
     * Get posts with fixed image paths for display
     */
    public function getPostsForDisplay($page = 1, $per_page = 10) {
        $posts = $this->getPosts($page, $per_page);
        return $this->fixImagePaths($posts);
    }

    /**
     * Get recent posts with fixed image paths for display
     */
    public function getRecentPostsForDisplay($limit = 5) {
        $posts = $this->getRecentPosts($limit);
        return $this->fixImagePaths($posts);
    }

    /**
     * Search posts with fixed image paths for display
     */
    public function searchPostsForDisplay($term, $page = 1, $per_page = 10) {
        $posts = $this->searchPosts($term, $page, $per_page);
        return $this->fixImagePaths($posts);
    }

    /**
     * Fix image paths for webserver compatibility (similar to testimonials)
     */    private function fixImagePaths($posts) {
        foreach ($posts as &$post) {
            // Fix featured image path for webserver compatibility
            if (!empty($post['featured_image'])) {
                // Ensure the path starts with uploads/ and is relative to root
                $path = $post['featured_image'];

                // Remove any leading slashes or dots
                $path = ltrim($path, './');

                // Ensure it starts with uploads/ if it's an uploaded image (PHP 7.4+ compatible)
                if (strpos($path, 'uploads/') !== 0 && strpos($path, 'images/') !== 0) {
                    $path = 'uploads/blog/' . basename($path);
                }

                // Ensure path starts with / for absolute path from root
                if (strpos($path, '/') !== 0) {
                    $path = '/' . $path;
                }

                $post['featured_image'] = $path;
            }
        }

        return $posts;
    }

    /**
     * Get posts by category with pagination
     */    public function getPostsByCategory($category_slug, $page = 1, $per_page = 10) {
        $offset = ($page - 1) * $per_page;
        $per_page = (int)$per_page;
        $offset = (int)$offset;

        return $this->db->select(
            "SELECT p.* FROM blog_posts p
            JOIN post_categories pc ON p.id = pc.post_id
            JOIN blog_categories c ON pc.category_id = c.id
            WHERE p.status = 'published'
            AND p.published_at <= NOW()
            AND c.slug = ?
            ORDER BY p.published_at DESC
            LIMIT $per_page OFFSET $offset",
            [$category_slug]
        );
    }

    /**
     * Count posts in a category
     */
    public function countPostsByCategory($category_slug) {
        $result = $this->db->selectOne(
            "SELECT COUNT(p.id) as count FROM blog_posts p
            JOIN post_categories pc ON p.id = pc.post_id
            JOIN blog_categories c ON pc.category_id = c.id
            WHERE p.status = 'published'
            AND p.published_at <= NOW()
            AND c.slug = ?",
            [$category_slug]
        );

        return $result ? $result['count'] : 0;
    }

    /**
     * Get posts by tag with pagination
     */    public function getPostsByTag($tag_slug, $page = 1, $per_page = 10) {
        $offset = ($page - 1) * $per_page;
        $per_page = (int)$per_page;
        $offset = (int)$offset;

        return $this->db->select(
            "SELECT p.* FROM blog_posts p
            JOIN post_tags pt ON p.id = pt.post_id
            JOIN blog_tags t ON pt.tag_id = t.id
            WHERE p.status = 'published'
            AND p.published_at <= NOW()
            AND t.slug = ?
            ORDER BY p.published_at DESC
            LIMIT $per_page OFFSET $offset",
            [$tag_slug]
        );
    }

    /**
     * Count posts with a tag
     */
    public function countPostsByTag($tag_slug) {
        $result = $this->db->selectOne(
            "SELECT COUNT(p.id) as count FROM blog_posts p
            JOIN post_tags pt ON p.id = pt.post_id
            JOIN blog_tags t ON pt.tag_id = t.id
            WHERE p.status = 'published'
            AND p.published_at <= NOW()
            AND t.slug = ?",
            [$tag_slug]
        );

        return $result ? $result['count'] : 0;
    }

    /**
     * Search posts with pagination
     */    public function searchPosts($term, $page = 1, $per_page = 10) {
        $offset = ($page - 1) * $per_page;
        $per_page = (int)$per_page;
        $offset = (int)$offset;
        $like_term = "%$term%";

        return $this->db->select(
            "SELECT * FROM blog_posts
            WHERE status = 'published'
            AND published_at <= NOW()
            AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)
            ORDER BY published_at DESC
            LIMIT $per_page OFFSET $offset",
            [$like_term, $like_term, $like_term]
        );
    }

    /**
     * Count search results
     */
    public function countSearchResults($term) {
        $like_term = "%$term%";

        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM blog_posts
            WHERE status = 'published'
            AND published_at <= NOW()
            AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)",
            [$like_term, $like_term, $like_term]
        );

        return $result ? $result['count'] : 0;
    }

    /**
     * Get all blog categories with post count
     */
    public function getCategories() {
        return $this->db->select(
            "SELECT c.*, COUNT(pc.post_id) as post_count
            FROM blog_categories c
            LEFT JOIN post_categories pc ON c.id = pc.category_id
            LEFT JOIN blog_posts p ON pc.post_id = p.id AND p.status = 'published' AND p.published_at <= NOW()
            GROUP BY c.id
            ORDER BY c.display_order ASC, c.name ASC"
        );
    }

    /**
     * Get a category by slug
     */
    public function getCategoryBySlug($slug) {
        return $this->db->selectOne(
            "SELECT * FROM blog_categories WHERE slug = ?",
            [$slug]
        );
    }

    /**
     * Get all blog tags with post count
     */
    public function getTags() {
        return $this->db->select(
            "SELECT t.*, COUNT(pt.post_id) as post_count
            FROM blog_tags t
            LEFT JOIN post_tags pt ON t.id = pt.tag_id
            LEFT JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published' AND p.published_at <= NOW()
            GROUP BY t.id
            ORDER BY t.name ASC"
        );
    }

    /**
     * Get a tag by slug
     */
    public function getTagBySlug($slug) {
        return $this->db->selectOne(
            "SELECT * FROM blog_tags WHERE slug = ?",
            [$slug]
        );
    }

    /**
     * Get categories for a post
     */
    public function getPostCategories($post_id) {
        return $this->db->select(
            "SELECT c.* FROM blog_categories c
            JOIN post_categories pc ON c.id = pc.category_id
            WHERE pc.post_id = ?
            ORDER BY c.name ASC",
            [$post_id]
        );
    }

    /**
     * Get tags for a post
     */
    public function getPostTags($post_id) {
        return $this->db->select(
            "SELECT t.* FROM blog_tags t
            JOIN post_tags pt ON t.id = pt.tag_id
            WHERE pt.post_id = ?
            ORDER BY t.name ASC",
            [$post_id]
        );
    }

    /**
     * Get author information
     */
    public function getAuthor($author_id) {
        if (!$author_id) {
            return null;
        }

        return $this->db->selectOne(
            "SELECT id, username, email FROM users WHERE id = ?",
            [$author_id]
        );
    }

    /**
     * Get related posts based on categories and tags
     */
    public function getRelatedPosts($post_id, $limit = 3) {
        // Get current post's categories
        $categories = $this->getPostCategories($post_id);
        if (empty($categories)) {
            return [];
        }        // Extract category IDs
        $category_ids = array_column($categories, 'id');
        $placeholders = implode(',', array_fill(0, count($category_ids), '?'));
        $limit = (int)$limit;

        // Get related posts
        $params = array_merge($category_ids, [$post_id]);

        return $this->db->select(
            "SELECT DISTINCT p.* FROM blog_posts p
            JOIN post_categories pc ON p.id = pc.post_id
            WHERE pc.category_id IN ($placeholders)
            AND p.id != ?
            AND p.status = 'published'
            AND p.published_at <= NOW()
            ORDER BY p.published_at DESC
            LIMIT $limit",
            $params
        );
    }

    /**
     * Get related posts with fixed image paths for display
     */
    public function getRelatedPostsForDisplay($post_id, $limit = 3) {
        $posts = $this->getRelatedPosts($post_id, $limit);
        return $this->fixImagePaths($posts);
    }

    /**
     * Get previous post
     */
    public function getPreviousPost($post_id) {
        $current_post = $this->getPostById($post_id);
        if (!$current_post) {
            return null;
        }

        return $this->db->selectOne(
            "SELECT * FROM blog_posts
            WHERE status = 'published'
            AND published_at <= NOW()
            AND published_at < ?
            ORDER BY published_at DESC
            LIMIT 1",
            [$current_post['published_at']]
        );
    }

    /**
     * Get next post
     */
    public function getNextPost($post_id) {
        $current_post = $this->getPostById($post_id);
        if (!$current_post) {
            return null;
        }

        return $this->db->selectOne(
            "SELECT * FROM blog_posts
            WHERE status = 'published'
            AND published_at <= NOW()
            AND published_at > ?
            ORDER BY published_at ASC
            LIMIT 1",
            [$current_post['published_at']]
        );
    }

    /**
     * Increment post view counter
     */
    public function incrementViews($post_id) {
        return $this->db->update(
            'blog_posts',
            ['views' => ['expr' => 'views + 1']],
            'id = ?',
            [$post_id]
        );
    }

    /**
     * Get approved comments for a post
     */
    public function getApprovedComments($post_id) {
        return $this->db->select(
            "SELECT * FROM blog_comments
            WHERE post_id = ?
            AND parent_id IS NULL
            AND status = 'approved'
            ORDER BY created_at ASC",
            [$post_id]
        );
    }

    /**
     * Get replies to a comment
     */
    public function getCommentReplies($comment_id) {
        return $this->db->select(
            "SELECT * FROM blog_comments
            WHERE parent_id = ?
            AND status = 'approved'
            ORDER BY created_at ASC",
            [$comment_id]
        );
    }

    /**
     * Add a new comment
     */
    public function addComment($data) {
        try {
            $comment_data = [
                'post_id' => $data['post_id'],
                'parent_id' => $data['parent_id'],
                'author_name' => trim($data['author_name']),
                'author_email' => trim($data['author_email']),
                'content' => trim($data['content']),
                'status' => 'pending' // All comments start as pending
            ];

            return $this->db->insert('blog_comments', $comment_data);
        } catch (Exception $e) {
            error_log("Error adding comment: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate a unique slug from a title or name
     */
    public function generateSlug($text) {
        // Convert to lowercase
        $slug = strtolower($text);

        // Replace non-alphanumeric characters with hyphens
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);

        // Replace spaces with hyphens
        $slug = preg_replace('/\s+/', '-', $slug);

        // Remove consecutive hyphens
        $slug = preg_replace('/-+/', '-', $slug);

        // Trim hyphens from beginning and end
        $slug = trim($slug, '-');

        // Check if slug already exists
        $original_slug = $slug;
        $count = 0;

        while ($this->slugExists($slug)) {
            $count++;
            $slug = $original_slug . '-' . $count;
        }

        return $slug;
    }

    /**
     * Check if a slug already exists
     */
    private function slugExists($slug, $tableName = 'blog_posts', $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM $tableName WHERE slug = ?";
        $params = [$slug];

        if ($excludeId !== null) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->selectOne($sql, $params);
        return $result && $result['count'] > 0;
    }

    /**
     * Add a new category
     */
    public function addCategory($data) {
        try {
            // Check if slug exists
            if ($this->slugExists($data['slug'], 'blog_categories')) {
                throw new Exception("A category with this slug already exists.");
            }

            $categoryData = [
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'] ?? null,
                'display_order' => $data['display_order'] ?? 0
            ];

            return $this->db->insert('blog_categories', $categoryData);
        } catch (Exception $e) {
            error_log("Error adding category: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a category
     */
    public function updateCategory($id, $data) {
        try {
            // Check if slug exists for other categories
            if ($this->slugExists($data['slug'], 'blog_categories', $id)) {
                throw new Exception("A category with this slug already exists.");
            }

            $categoryData = [
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'] ?? null,
                'display_order' => $data['display_order'] ?? 0
            ];

            $this->db->update('blog_categories', $categoryData, 'id = ?', [$id]);
            return true;
        } catch (Exception $e) {
            error_log("Error updating category: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a category
     */
    public function deleteCategory($id) {
        try {
            // Start transaction
            $this->db->getConnection()->beginTransaction();

            // Delete category associations with posts
            $this->db->delete('post_categories', 'category_id = ?', [$id]);

            // Delete the category
            $this->db->delete('blog_categories', 'id = ?', [$id]);

            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error deleting category: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a category by ID
     */
    public function getCategoryById($id) {
        return $this->db->selectOne(
            "SELECT * FROM blog_categories WHERE id = ?",
            [$id]
        );
    }

    /**
     * Add a new tag
     */
    public function addTag($data) {
        try {
            // Check if slug exists
            if ($this->slugExists($data['slug'], 'blog_tags')) {
                throw new Exception("A tag with this slug already exists.");
            }

            $tagData = [
                'name' => $data['name'],
                'slug' => $data['slug']
            ];

            return $this->db->insert('blog_tags', $tagData);
        } catch (Exception $e) {
            error_log("Error adding tag: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a tag
     */
    public function updateTag($id, $data) {
        try {
            // Check if slug exists for other tags
            if ($this->slugExists($data['slug'], 'blog_tags', $id)) {
                throw new Exception("A tag with this slug already exists.");
            }

            $tagData = [
                'name' => $data['name'],
                'slug' => $data['slug']
            ];

            $this->db->update('blog_tags', $tagData, 'id = ?', [$id]);
            return true;
        } catch (Exception $e) {
            error_log("Error updating tag: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a tag
     */
    public function deleteTag($id) {
        try {
            // Start transaction
            $this->db->getConnection()->beginTransaction();

            // Delete tag associations with posts
            $this->db->delete('post_tags', 'tag_id = ?', [$id]);

            // Delete the tag
            $this->db->delete('blog_tags', 'id = ?', [$id]);

            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error deleting tag: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a tag by ID
     */
    public function getTagById($id) {
        return $this->db->selectOne(
            "SELECT * FROM blog_tags WHERE id = ?",
            [$id]
        );
    }

    /**
     * Add a new blog post
     */
    public function addPost($data) {
        try {
            // Start transaction
            $this->db->getConnection()->beginTransaction();

            // Check if slug exists
            if ($this->slugExists($data['slug'])) {
                throw new Exception("A post with this slug already exists.");
            }

            // Prepare post data
            $postData = [
                'title' => $data['title'],
                'slug' => $data['slug'],
                'excerpt' => $data['excerpt'],
                'content' => $data['content'],
                'status' => $data['status'],
                'featured_image' => $data['featured_image'] ?? null,
                'author_id' => $data['author_id'],
                'views' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Add published_at date if status is published
            if ($data['status'] === 'published') {
                $postData['published_at'] = $data['published_at'] ?? date('Y-m-d H:i:s');
            }

            // Insert post and get new post ID
            $postId = $this->db->insert('blog_posts', $postData);

            // Add categories
            if (!empty($data['category_ids'])) {
                foreach ($data['category_ids'] as $categoryId) {
                    $this->db->insert('post_categories', [
                        'post_id' => $postId,
                        'category_id' => $categoryId
                    ]);
                }
            }

            // Add tags
            if (!empty($data['tag_ids'])) {
                foreach ($data['tag_ids'] as $tagId) {
                    $this->db->insert('post_tags', [
                        'post_id' => $postId,
                        'tag_id' => $tagId
                    ]);
                }
            }

            $this->db->getConnection()->commit();
            return $postId;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error adding post: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a blog post
     */
    public function updatePost($id, $data) {
        try {
            // Enhanced logging
            error_log("Starting blog post update for ID: " . $id);
            error_log("Update data: " . print_r($data, true));

            // Validate required fields
            if (empty($data['title'])) {
                throw new Exception("Title is required");
            }
            if (empty($data['excerpt'])) {
                throw new Exception("Excerpt is required");
            }
            if (empty($data['content'])) {
                throw new Exception("Content is required");
            }

            // Validate post exists
            $existingPost = $this->getAdminPostById($id);
            if (!$existingPost) {
                throw new Exception("Blog post not found with ID: $id");
            }

            // Start transaction
            $this->db->getConnection()->beginTransaction();

            // Check if slug exists for other posts
            if ($this->slugExists($data['slug'], 'blog_posts', $id)) {
                throw new Exception("A post with this slug already exists.");
            }

            // Prepare post data
            $postData = [
                'title' => $data['title'],
                'slug' => $data['slug'],
                'excerpt' => $data['excerpt'],
                'content' => $data['content'],
                'status' => $data['status'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add author_id if provided (for updates, we might want to keep the original author)
            if (isset($data['author_id']) && !empty($data['author_id'])) {
                $postData['author_id'] = $data['author_id'];
            }

            // Handle featured image (including removal)
            if (isset($data['featured_image'])) {
                $postData['featured_image'] = $data['featured_image'];
                error_log("Setting featured_image to: " . ($data['featured_image'] ?? 'NULL'));
            }

            // Update published_at date if status is published
            if ($data['status'] === 'published') {
                $currentPost = $this->getAdminPostById($id);

                // If post is being published for the first time
                if ($currentPost['status'] !== 'published' || $currentPost['published_at'] === null) {
                    $postData['published_at'] = date('Y-m-d H:i:s');
                }
            }

            // Update post
            error_log("About to update blog post with data: " . print_r($postData, true));
            $updateResult = $this->db->update('blog_posts', $postData, 'id = ?', [$id]);
            error_log("Update result: " . ($updateResult ? 'SUCCESS' : 'FAILED'));

            if (!$updateResult) {
                throw new Exception("Failed to update blog post in database. Check error logs for details.");
            }

            // Delete existing categories and tags
            $this->db->delete('post_categories', 'post_id = ?', [$id]);
            $this->db->delete('post_tags', 'post_id = ?', [$id]);

            // Add categories
            if (!empty($data['category_ids'])) {
                foreach ($data['category_ids'] as $categoryId) {
                    $this->db->insert('post_categories', [
                        'post_id' => $id,
                        'category_id' => $categoryId
                    ]);
                }
            }

            // Add tags
            if (!empty($data['tag_ids'])) {
                foreach ($data['tag_ids'] as $tagId) {
                    $this->db->insert('post_tags', [
                        'post_id' => $id,
                        'tag_id' => $tagId
                    ]);
                }
            }

            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error updating post: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a blog post
     */
    public function deletePost($id) {
        try {
            // Start transaction
            $this->db->getConnection()->beginTransaction();

            // Delete post associations (categories, tags, comments)
            $this->db->delete('post_categories', 'post_id = ?', [$id]);
            $this->db->delete('post_tags', 'post_id = ?', [$id]);
            $this->db->delete('blog_comments', 'post_id = ?', [$id]);

            // Delete the post
            $result = $this->db->delete('blog_posts', 'id = ?', [$id]);

            if (!$result) {
                throw new Exception("Failed to delete blog post. Post ID may not exist.");
            }

            $this->db->getConnection()->commit();
            return true;
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("Error deleting post: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a post by ID for admin (includes all statuses)
     */
    public function getAdminPostById($id) {
        return $this->db->selectOne(
            "SELECT * FROM blog_posts WHERE id = ?",
            [$id]
        );
    }

    /**
     * Get admin posts with filters and pagination
     */
    public function getAdminPosts($search = '', $status = '', $category = 0, $page = 1, $per_page = 10) {
        $offset = ($page - 1) * $per_page;
        $per_page = (int)$per_page;
        $offset = (int)$offset;

        $sql = "SELECT DISTINCT p.* FROM blog_posts p";
        $params = [];

        // Join with categories if needed
        if ($category > 0) {
            $sql .= " JOIN post_categories pc ON p.id = pc.post_id";
        }

        $sql .= " WHERE 1=1";

        // Add search condition
        if ($search) {
            $sql .= " AND (p.title LIKE ? OR p.content LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        // Add status condition
        if ($status) {
            $sql .= " AND p.status = ?";
            $params[] = $status;
        }

        // Add category condition
        if ($category > 0) {
            $sql .= " AND pc.category_id = ?";
            $params[] = $category;
        }

        // Use the actual integers in the query, not as parameters
        $sql .= " ORDER BY p.created_at DESC LIMIT $per_page OFFSET $offset";

        return $this->db->select($sql, $params);
    }

    /**
     * Count admin posts with filters
     */
    public function countAdminPosts($search = '', $status = '', $category = 0) {
        $sql = "SELECT COUNT(DISTINCT p.id) as count FROM blog_posts p";
        $params = [];

        // Join with categories if needed
        if ($category > 0) {
            $sql .= " JOIN post_categories pc ON p.id = pc.post_id";
        }

        $sql .= " WHERE 1=1";

        // Add search condition
        if ($search) {
            $sql .= " AND (p.title LIKE ? OR p.content LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        // Add status condition
        if ($status) {
            $sql .= " AND p.status = ?";
            $params[] = $status;
        }

        // Add category condition
        if ($category > 0) {
            $sql .= " AND pc.category_id = ?";
            $params[] = $category;
        }

        $result = $this->db->selectOne($sql, $params);
        return $result ? $result['count'] : 0;
    }

    /**
     * Update post status
     */
    public function updatePostStatus($postId, $status) {
        try {
            $postData = [
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // If publishing, set published_at date
            if ($status === 'published') {
                $post = $this->getAdminPostById($postId);

                // If not previously published
                if ($post['published_at'] === null) {
                    $postData['published_at'] = date('Y-m-d H:i:s');
                }
            }

            $result = $this->db->update('blog_posts', $postData, 'id = ?', [$postId]);
            if (!$result) {
                throw new Exception("Failed to update post status");
            }
            return true;
        } catch (Exception $e) {
            error_log("Error updating post status: " . $e->getMessage());
            throw $e;
        }
    }
}