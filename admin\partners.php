<?php
require_once 'includes/auth.php';
require_once '../includes/Partners.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: index.php');
    exit();
}

$partnersHandler = new Partners();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                if (isset($_FILES['logo']) && $_FILES['logo']['error'] === 0) {
                    try {
                        $logoPath = $partnersHandler->handleLogoUpload($_FILES['logo']);
                        $result = $partnersHandler->addPartner(
                            $_POST['name'],
                            $logoPath,
                            $_POST['website_url'] ?: null,
                            $_POST['description'] ?: null,
                            intval($_POST['display_order'])
                        );

                        if ($result) {
                            $message = 'Partner added successfully!';
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to add partner.';
                            $messageType = 'error';
                        }
                    } catch (Exception $e) {
                        $message = 'Error: ' . $e->getMessage();
                        $messageType = 'error';
                    }
                } else {
                    $message = 'Please select a logo image.';
                    $messageType = 'error';
                }
                break;
                
            case 'edit':
                $logoPath = $_POST['current_logo'];
                
                // Handle new logo upload if provided
                if (isset($_FILES['logo']) && $_FILES['logo']['error'] === 0) {
                    try {
                        $newLogoPath = $partnersHandler->handleLogoUpload($_FILES['logo']);
                        // Delete old logo if it exists
                        if (file_exists($logoPath)) {
                            unlink($logoPath);
                        }
                        $logoPath = $newLogoPath;
                    } catch (Exception $e) {
                        $message = 'Error uploading new logo: ' . $e->getMessage();
                        $messageType = 'error';
                        break;
                    }
                }
                  $result = $partnersHandler->updatePartner(
                    intval($_POST['partner_id']),
                    $_POST['name'],
                    $logoPath,
                    $_POST['website_url'] ?: null,
                    $_POST['description'] ?: null,
                    intval($_POST['display_order']),
                    $_POST['status']
                );
                
                if ($result) {
                    $message = 'Partner updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update partner.';
                    $messageType = 'error';
                }
                break;
                
            case 'delete':
                $result = $partnersHandler->deletePartner(intval($_POST['partner_id']));
                if ($result) {
                    $message = 'Partner deleted successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to delete partner.';
                    $messageType = 'error';
                }
                break;
                
            case 'toggle_status':
                $result = $partnersHandler->togglePartnerStatus(intval($_POST['partner_id']));
                if ($result) {
                    $message = 'Partner status updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update partner status.';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get all partners and stats
$partners = $partnersHandler->getAllPartners(true);
$stats = $partnersHandler->getStats();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partners Management - Doctors At Door Step</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--dark-color);
            color: var(--white);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header h1 {
            font-size: 20px;
            margin-bottom: 5px;
            color: var(--primary-color);
        }
        
        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .menu-item:hover, .menu-item.active {
            background-color: rgba(44, 123, 229, 0.2);
            color: var(--primary-color);
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: absolute;
            bottom: 0;
            width: 100%;
        }
        
        .logout-btn {
            display: block;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }
        
        .content-header {
            background-color: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }
        
        .content-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            color: var(--secondary-color);
        }
        
        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: var(--secondary-color);
        }
        
        /* Cards */
        .card {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            border: none;
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: transparent;
            border-bottom: 1px solid #e9ecef;
            padding: 20px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* Stats Cards */
        .small-box {
            border-radius: 10px;
            color: white;
            padding: 20px;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .small-box.bg-info { background: linear-gradient(45deg, var(--primary-color), #3B82F6); }
        .small-box.bg-success { background: linear-gradient(45deg, var(--success-color), #15803D); }
        .small-box.bg-warning { background: linear-gradient(45deg, #F59E0B, #D97706); }
        
        .small-box .inner h3 {
            font-size: 38px;
            font-weight: 700;
            margin: 0;
        }
        
        .small-box .inner p {
            font-size: 16px;
            margin: 0;
            opacity: 0.9;
        }
        
        .small-box .icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 50px;
            opacity: 0.3;
        }
        
        /* Tables */
        .table {
            background-color: var(--white);
        }
        
        .table th {
            background-color: var(--light-color);
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .table td {
            vertical-align: middle;
        }
        
        /* Buttons */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1a65d1;
            border-color: #1a65d1;
        }
        
        /* Badges */
        .badge {
            font-size: 12px;
            padding: 6px 10px;
        }
        
        .badge-success {
            background-color: var(--success-color);
        }
        
        .badge-secondary {
            background-color: var(--secondary-color);
        }
        
        /* Alerts */
        .alert {
            border-radius: 8px;
            border: none;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: #F0FDF4;
            color: #15803D;
        }
        
        .alert-danger {
            background-color: #FEF2F2;
            color: #DC2626;
        }
        
        /* Modal */
        .modal-content {
            border-radius: 10px;
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .modal-header {
            border-bottom: 1px solid #e9ecef;
            padding: 20px;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            border-top: 1px solid #e9ecef;
            padding: 20px;
        }
        
        /* Form styles */
        .form-control {
            border-radius: 6px;
            border: 1px solid #d1d5db;
            padding: 10px 15px;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 123, 229, 0.25);
        }
        
        /* Responsive */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">        <!-- Content Header -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">Partners Management</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                            <li class="breadcrumb-item active">Partners</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container-fluid">
            <!-- Display Messages -->
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
            <?php endif; ?>

            <!-- Debug Information -->
            <?php if (isset($_SESSION['debug_info']) && !empty($_SESSION['debug_info'])): ?>
            <div class="alert alert-info alert-dismissible fade show">
                <h5>Debug Information:</h5>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px; max-height: 300px; overflow-y: auto;">
<?php
foreach ($_SESSION['debug_info'] as $info) {
    echo htmlspecialchars($info) . "\n";
}
unset($_SESSION['debug_info']); // Clear debug info after displaying
?>
                </pre>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
            <?php endif; ?>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?php echo $stats['total']; ?></h3>
                            <p>Total Partners</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?php echo $stats['active']; ?></h3>
                            <p>Active Partners</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3><?php echo $stats['inactive']; ?></h3>
                            <p>Inactive Partners</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add New Partner Button -->
            <div class="row mb-3">
                <div class="col-12">
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addPartnerModal">
                        <i class="fas fa-plus"></i> Add New Partner
                    </button>
                </div>
            </div>

            <!-- Partners Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">All Partners</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Logo</th>
                                    <th>Name</th>
                                    <th>Website</th>
                                    <th>Display Order</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($partners)): ?>
                                <tr>
                                    <td colspan="8" class="text-center">No partners found.</td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($partners as $partner): ?>
                                <tr>
                                    <td><?php echo $partner['id']; ?></td>                                    <td>
                                        <img src="../<?php echo htmlspecialchars($partner['logo']); ?>" 
                                             alt="<?php echo htmlspecialchars($partner['name']); ?>"
                                             style="width: 50px; height: 30px; object-fit: contain;">
                                    </td>
                                    <td><?php echo htmlspecialchars($partner['name']); ?></td>
                                    <td>
                                        <?php if ($partner['website_url']): ?>
                                        <a href="<?php echo htmlspecialchars($partner['website_url']); ?>" target="_blank">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $partner['display_order']; ?></td>                                    <td>
                                        <span class="badge badge-<?php echo $partner['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($partner['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($partner['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="editPartner(<?php echo htmlspecialchars(json_encode($partner)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="partner_id" value="<?php echo $partner['id']; ?>">                                                <button type="submit" class="btn btn-sm btn-<?php echo $partner['status'] === 'active' ? 'warning' : 'success'; ?>"
                                                        onclick="return confirm('Toggle partner status?')">
                                                    <i class="fas fa-<?php echo $partner['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                            </form>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="partner_id" value="<?php echo $partner['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                        onclick="return confirm('Are you sure you want to delete this partner?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>        </div>
    </div>

    <!-- Add Partner Modal -->
    <div class="modal fade" id="addPartnerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Add New Partner</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="form-group">
                            <label for="name">Partner Name *</label>
                            <input type="text" class="form-control" name="name" id="name" required>
                        </div>

                        <div class="form-group">
                            <label for="logo">Logo Image *</label>
                            <input type="file" class="form-control-file" name="logo" id="logo" accept="image/*" required>
                            <small class="form-text text-muted">Supported formats: JPG, PNG, GIF, WebP. Max size: 5MB</small>
                        </div>

                        <div class="form-group">
                            <label for="website_url">Website URL</label>
                            <input type="url" class="form-control" name="website_url" id="website_url" placeholder="https://example.com">
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" name="description" id="description" rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="display_order">Display Order</label>
                            <input type="number" class="form-control" name="display_order" id="display_order" value="0" min="0">
                            <small class="form-text text-muted">Lower numbers appear first</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Partner</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Partner Modal -->
    <div class="modal fade" id="editPartnerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Edit Partner</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="partner_id" id="edit_partner_id">
                        <input type="hidden" name="current_logo" id="edit_current_logo">
                        
                        <div class="form-group">
                            <label for="edit_name">Partner Name *</label>
                            <input type="text" class="form-control" name="name" id="edit_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Current Logo</label>
                            <div>
                                <img id="edit_current_logo_img" src="" alt="Current Logo" style="max-width: 200px; max-height: 100px; object-fit: contain;">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_logo">New Logo Image (optional)</label>
                            <input type="file" class="form-control-file" name="logo" id="edit_logo" accept="image/*">
                            <small class="form-text text-muted">Leave empty to keep current logo</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_website_url">Website URL</label>
                            <input type="url" class="form-control" name="website_url" id="edit_website_url">
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_description">Description</label>
                            <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_display_order">Display Order</label>
                            <input type="number" class="form-control" name="display_order" id="edit_display_order" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_status">Status</label>
                            <select class="form-control" name="status" id="edit_status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Partner</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function editPartner(partner) {
        document.getElementById('edit_partner_id').value = partner.id;
        document.getElementById('edit_name').value = partner.name;
        document.getElementById('edit_website_url').value = partner.website_url || '';
        document.getElementById('edit_description').value = partner.description || '';
        document.getElementById('edit_display_order').value = partner.display_order;
        document.getElementById('edit_status').value = partner.status;
        document.getElementById('edit_current_logo').value = partner.logo;
        document.getElementById('edit_current_logo_img').src = '../' + partner.logo;
        
        $('#editPartnerModal').modal('show');
    }

    // Logo upload validation
    function validateLogoUpload(input) {
        const file = input.files[0];
        if (!file) return true;
        
        // Check file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            input.value = '';
            return false;
        }
        
        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please upload a valid image file (JPG, PNG, GIF, WebP)');
            input.value = '';
            return false;
        }
        
        return true;
    }

    // Add event listeners for file inputs
    document.addEventListener('DOMContentLoaded', function() {
        const fileInputs = document.querySelectorAll('input[type="file"][name="logo"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', function() {
                validateLogoUpload(this);
            });
        });
    });
    </script>
</body>
</html>
