<?php
session_start();
require_once 'includes/auth.php';
require_admin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

require_once '../includes/Database.php';
$db = Database::getInstance();

// Initialize message variables
$message = '';
$messageType = '';

// Handle status update
if (isset($_GET['action']) && $_GET['action'] == 'update_status' && isset($_GET['id']) && isset($_GET['status'])) {
    $id = (int)$_GET['id'];
    $status = $_GET['status'];

    // Validate status
    $valid_statuses = ['pending', 'confirmed', 'completed', 'cancelled'];
    if (in_array($status, $valid_statuses)) {
        try {
            $db->update('bookings', ['status' => $status], "id = $id");
            $message = "Booking status updated successfully!";
            $messageType = "success";
        } catch (Exception $e) {
            $message = "Error updating booking status: " . $e->getMessage();
            $messageType = "error";
        }
    } else {
        $message = "Invalid status value!";
        $messageType = "error";
    }
}

// Handle delete
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];

    try {
        $db->delete('bookings', "id = $id");
        $message = "Booking deleted successfully!";
        $messageType = "success";
    } catch (Exception $e) {
        $message = "Error deleting booking: " . $e->getMessage();
        $messageType = "error";
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';    if ($action === 'add' || $action === 'edit') {
        $bookingData = [
            'client_name' => $_POST['client_name'],
            'client_email' => $_POST['client_email'],
            'client_phone' => $_POST['client_phone'],
            'service_id' => (int)$_POST['service_id'],
            'booking_date' => $_POST['booking_date'],
            'booking_time' => $_POST['booking_time'],
            'address' => $_POST['address'],
            'special_requests' => $_POST['special_requests'],
            'status' => $_POST['status']
        ];

        // Handle reference number
        if ($action === 'add') {
            // For new bookings, use provided reference or generate one
            if (!empty($_POST['reference'])) {
                $bookingData['reference'] = trim($_POST['reference']);
            } else {
                // Generate a new reference number
                $bookingData['reference'] = 'BK' . date('Ymd') . strtoupper(substr(uniqid(), -6));
            }
        }
        // For edit action, we don't update the reference number (it's readonly in the form)

        try {
            if ($action === 'add') {
                $db->insert('bookings', $bookingData);
                $message = "Booking added successfully!";
                $messageType = "success";
            } else {
                $id = (int)$_POST['id'];
                $db->update('bookings', $bookingData, "id = $id");
                $message = "Booking updated successfully!";
                $messageType = "success";
            }

            // Redirect to list view after successful operation
            header('Location: bookings.php?message=' . urlencode($message) . '&messageType=' . urlencode($messageType));
            exit;
        } catch (Exception $e) {
            $message = "Error processing booking: " . $e->getMessage();
            $messageType = "error";
        }
    }
}

// Check for message in URL (after redirect)
if (isset($_GET['message']) && isset($_GET['messageType'])) {
    $message = $_GET['message'];
    $messageType = $_GET['messageType'];
}

// Determine if we're viewing or listing bookings
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$bookingId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get booking data for viewing
$bookingData = [];
if (($action === 'view' || $action === 'edit') && $bookingId > 0) {
    try {
        // First try to get the booking directly without the join
        $bookingData = $db->selectOne("
            SELECT * FROM bookings WHERE id = ?",
            [$bookingId]
        );

        if (!empty($bookingData)) {
            // If we found the booking, try to get the service name if service_id exists
            if (!empty($bookingData['service_id'])) {
                $serviceData = $db->selectOne("
                    SELECT title FROM services WHERE id = ?",
                    [$bookingData['service_id']]
                );

                if ($serviceData) {
                    $bookingData['service_name'] = $serviceData['title'];
                } else {
                    $bookingData['service_name'] = 'Unknown Service';
                }
            } else {
                // Extract service from special_requests if available
                if (!empty($bookingData['special_requests']) && strpos($bookingData['special_requests'], 'Service:') !== false) {
                    $lines = explode("\n", $bookingData['special_requests']);
                    foreach ($lines as $line) {
                        if (strpos($line, 'Service:') === 0) {
                            $bookingData['service_name'] = trim(str_replace('Service:', '', $line));
                            break;
                        }
                    }
                }

                // If we still don't have a service name, use a default
                if (empty($bookingData['service_name'])) {
                    $bookingData['service_name'] = 'Service Not Specified';
                }
            }
        } else {
            $message = 'Booking not found!';
            $messageType = 'error';
            $action = 'list';
        }
    } catch (Exception $e) {
        $message = "Error retrieving booking: " . $e->getMessage();
        $messageType = "error";
        $action = 'list';
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$reference_filter = isset($_GET['reference']) ? trim($_GET['reference']) : '';

// Build query for listing
$query = "SELECT b.* FROM bookings b WHERE 1=1";
$params = [];

if (!empty($status_filter)) {
    $query .= " AND b.status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($date_filter)) {
    $query .= " AND b.booking_date = :date";
    $params[':date'] = $date_filter;
}

if (!empty($reference_filter)) {
    $query .= " AND b.reference LIKE :reference";
    $params[':reference'] = "%$reference_filter%";
}

if (!empty($search)) {
    $query .= " AND (b.client_name LIKE :search OR b.client_email LIKE :search OR b.client_phone LIKE :search OR b.reference LIKE :search)";
    $params[':search'] = "%$search%";
}

$query .= " ORDER BY b.booking_date DESC, b.booking_time DESC";

// Get bookings for listing
$bookings = [];
if ($action === 'list') {
    try {
        $bookings = $db->select($query, $params);
    } catch (Exception $e) {
        $message = "Error retrieving bookings: " . $e->getMessage();
        $messageType = "error";
    }
}

// Get unique dates for filter
try {
    $dates = $db->select("SELECT DISTINCT booking_date FROM bookings ORDER BY booking_date DESC");
} catch (Exception $e) {
    $dates = [];
}

// Get services for dropdown
try {
    $services = $db->select("SELECT id, title FROM services WHERE is_active = 1 ORDER BY title");
} catch (Exception $e) {
    $services = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Bookings - Doctors At Door Step</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2C7BE5;
            --secondary-color: #6B7A99;
            --dark-color: #1A2B3C;
            --light-color: #F8FAFC;
            --white: #FFFFFF;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --sidebar-width: 250px;
            --success-color: #10B981;
            --error-color: #EF4444;
            --warning-color: #F59E0B;
            --info-color: #3B82F6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--dark-color);
            color: var(--white);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 20px;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: rgba(44, 123, 229, 0.2);
            color: var(--primary-color);
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: absolute;
            bottom: 0;
            width: 100%;
        }

        .logout-btn {
            display: block;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        .add-new-btn {
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s;
        }

        .add-new-btn:hover {
            background-color: var(--dark-color);
        }

        /* Alert Messages */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        /* Filter Section */
        .filter-section {
            background-color: var(--white);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
        }        .filter-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: flex-end;
        }

        .filter-group {
            min-width: 150px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--dark-color);
            font-size: 13px;
        }

        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(44, 123, 229, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .filter-buttons .btn-submit,
        .filter-buttons .btn-cancel {
            padding: 10px 15px;
            font-size: 13px;
            white-space: nowrap;
        }

        /* Responsive filter adjustments */
        @media (max-width: 1200px) {
            .filter-form {
                grid-template-columns: 1fr 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .filter-form {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                justify-content: center;
                margin-top: 10px;
            }
        }/* Bookings Table */
        .bookings-table {
            width: 100%;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            overflow: visible;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .bookings-table table {
            width: 100%;
            border-collapse: collapse;
            overflow: visible;
        }

        .bookings-table th, .bookings-table td {
            padding: 16px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            vertical-align: middle;
            position: relative;
            overflow: visible;
        }

        .bookings-table th {
            background-color: #f8fafc;
            font-weight: 600;
            font-size: 13px;
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bookings-table tr:last-child td {
            border-bottom: none;
        }

        .bookings-table tbody tr {
            transition: all 0.2s ease;
            position: relative;
        }

        .bookings-table tbody tr:hover {
            background-color: rgba(44, 123, 229, 0.03);
        }        .booking-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background-color: rgba(245, 158, 11, 0.1);
            color: #d97706;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-confirmed {
            background-color: rgba(59, 130, 246, 0.1);
            color: #2563eb;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .status-completed {
            background-color: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }        .status-cancelled {
            background-color: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Status Dropdown */
        .status-dropdown {
            position: relative;
            display: inline-block;
        }

        .status-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: var(--white);
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-top: 5px;
            overflow: hidden;
        }

        .status-dropdown-content::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 15px;
            width: 12px;
            height: 12px;
            background-color: var(--white);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: none;
            border-right: none;
            transform: rotate(45deg);
        }

        .status-dropdown:hover .status-dropdown-content,
        .status-dropdown.active .status-dropdown-content {
            display: block;
            animation: dropdownFadeIn 0.2s ease-out forwards;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-dropdown-content a {
            color: var(--dark-color);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .status-dropdown-content a:last-child {
            border-bottom: none;
        }

        .status-dropdown-content a:hover {
            background-color: #f8fafc;
            color: var(--primary-color);
        }

        .status-dropdown-content a i {
            width: 16px;
            text-align: center;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-view, .btn-edit, .btn-delete {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-view {
            background-color: rgba(107, 122, 153, 0.1);
            color: var(--secondary-color);
        }

        .btn-view:hover {
            background-color: var(--secondary-color);
            color: var(--white);
        }

        .btn-edit {
            background-color: rgba(44, 123, 229, 0.1);
            color: var(--primary-color);
        }

        .btn-edit:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-delete {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }        .btn-delete:hover {
            background-color: var(--error-color);
            color: var(--white);
        }

        /* Reference Badge */
        .reference-badge {
            background: linear-gradient(135deg, var(--primary-color), #1e5bb8);
            color: var(--white);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(44, 123, 229, 0.3);
        }        /* Text utilities */
        .text-muted {
            color: var(--secondary-color);
            font-style: italic;
        }

        /* Reference Display in Detail View */
        .booking-title-section {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .reference-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .reference-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--secondary-color);
        }

        .reference-number {
            background: linear-gradient(135deg, var(--primary-color), #1e5bb8);
            color: var(--white);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 3px 6px rgba(44, 123, 229, 0.3);
        }

        /* Booking Status Styles */
        .booking-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }

        .status-confirmed {
            background-color: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }

        .status-completed {
            background-color: rgba(59, 130, 246, 0.2);
            color: var(--info-color);
        }

        .status-cancelled {
            background-color: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 50px 20px;
        }

        .empty-state i {
            font-size: 48px;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .empty-state h3 {
            margin-bottom: 10px;
        }

        .empty-state p {
            color: var(--secondary-color);
            max-width: 500px;
            margin: 0 auto;
        }

        /* Booking Detail */
        .booking-detail {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            padding: 20px;
        }

        .booking-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .booking-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .booking-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 14px;
            color: var(--secondary-color);
        }

        .booking-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .booking-info {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-group {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--secondary-color);
        }

        .info-value {
            font-size: 16px;
        }

        .booking-notes {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--light-color);
            border-radius: 5px;
        }

        .booking-notes h3 {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .booking-actions {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* Booking Form */
        .booking-form {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-cancel {
            padding: 10px 20px;
            background-color: var(--light-color);
            color: var(--dark-color);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-cancel:hover {
            background-color: #e0e0e0;
        }

        .btn-submit {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-submit:hover {
            background-color: var(--dark-color);
        }

        /* Form Helpers */
        .text-muted {
            color: var(--secondary-color);
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }

        .text-center {
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .toggle-sidebar {
                display: block;
            }

            .bookings-table {
                overflow-x: auto;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <?php if ($action === 'view'): ?>
                    Booking Details
                <?php elseif ($action === 'edit'): ?>
                    Edit Booking
                <?php elseif ($action === 'add'): ?>
                    Add New Booking
                <?php else: ?>
                    Manage Bookings
                <?php endif; ?>
            </h1>

            <?php if ($action === 'list'): ?>
                <a href="bookings.php?action=add" class="add-new-btn">
                    <i class="fas fa-plus"></i> Add New Booking
                </a>
            <?php endif; ?>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>            <!-- Filter Section -->
            <div class="filter-section">
                <form action="" method="GET" class="filter-form">
                    <div class="filter-group">
                        <label for="reference">Reference Number</label>
                        <input type="text" name="reference" id="reference" placeholder="Enter reference number" value="<?php echo htmlspecialchars($reference_filter); ?>">
                    </div>
                    <div class="filter-group">
                        <label for="search">Search</label>
                        <input type="text" name="search" id="search" placeholder="Search by name, email, phone, or reference" value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="filter-group">
                        <label for="status">Filter by Status</label>
                        <select name="status" id="status">
                            <option value="">All Statuses</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="confirmed" <?php echo $status_filter === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date">Filter by Date</label>
                        <select name="date" id="date">
                            <option value="">All Dates</option>
                            <?php foreach ($dates as $date): ?>
                                <option value="<?php echo $date['booking_date']; ?>" <?php echo $date_filter === $date['booking_date'] ? 'selected' : ''; ?>>
                                    <?php echo date('F j, Y', strtotime($date['booking_date'])); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-buttons">
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                        <a href="bookings.php" class="btn-cancel">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </form>
            </div>

            <!-- Bookings List -->
            <div class="bookings-table">
                <table>                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Reference</th>
                            <th>Client</th>
                            <th>Service</th>
                            <th>Date & Time</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>                        <?php if (empty($bookings)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No bookings found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($bookings as $booking): ?>
                                <tr>
                                    <td><?php echo $booking['id']; ?></td>
                                    <td>
                                        <?php if (!empty($booking['reference'])): ?>
                                            <span class="reference-badge"><?php echo htmlspecialchars($booking['reference']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div><?php echo htmlspecialchars($booking['client_name']); ?></div>
                                        <small><?php echo htmlspecialchars($booking['client_email']); ?></small><br>
                                        <small><?php echo htmlspecialchars($booking['client_phone']); ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        // Try to get service name from service_id
                                        if (!empty($booking['service_id'])) {
                                            try {
                                                $serviceData = $db->selectOne("SELECT title FROM services WHERE id = ?", [$booking['service_id']]);
                                                echo htmlspecialchars($serviceData['title'] ?? 'Unknown Service');
                                            } catch (Exception $e) {
                                                echo 'Unknown Service';
                                            }
                                        }
                                        // Try to extract service from special_requests
                                        else if (!empty($booking['special_requests']) && strpos($booking['special_requests'], 'Service:') !== false) {
                                            $lines = explode("\n", $booking['special_requests']);
                                            $serviceName = 'Service Not Specified';
                                            foreach ($lines as $line) {
                                                if (strpos($line, 'Service:') === 0) {
                                                    $serviceName = trim(str_replace('Service:', '', $line));
                                                    break;
                                                }
                                            }
                                            echo htmlspecialchars($serviceName);
                                        }
                                        // Default fallback
                                        else {
                                            echo 'Service Not Specified';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <div><?php echo date('M d, Y', strtotime($booking['booking_date'])); ?></div>
                                        <small><?php echo date('g:i A', strtotime($booking['booking_time'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="status-dropdown">
                                            <span class="booking-status status-<?php echo $booking['status']; ?>">
                                                <?php echo ucfirst($booking['status']); ?>
                                            </span>                                            <div class="status-dropdown-content">
                                                <a href="bookings.php?action=update_status&id=<?php echo $booking['id']; ?>&status=pending">
                                                    <i class="fas fa-clock"></i> Mark as Pending
                                                </a>
                                                <a href="bookings.php?action=update_status&id=<?php echo $booking['id']; ?>&status=confirmed">
                                                    <i class="fas fa-check-circle"></i> Mark as Confirmed
                                                </a>
                                                <a href="bookings.php?action=update_status&id=<?php echo $booking['id']; ?>&status=completed">
                                                    <i class="fas fa-check-double"></i> Mark as Completed
                                                </a>
                                                <a href="bookings.php?action=update_status&id=<?php echo $booking['id']; ?>&status=cancelled">
                                                    <i class="fas fa-times-circle"></i> Mark as Cancelled
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="bookings.php?action=view&id=<?php echo $booking['id']; ?>" class="btn-view">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="bookings.php?action=edit&id=<?php echo $booking['id']; ?>" class="btn-edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="#" class="btn-delete" onclick="confirmDelete(<?php echo $booking['id']; ?>, '<?php echo htmlspecialchars($booking['client_name']); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php elseif ($action === 'view'): ?>            <!-- Booking Detail View -->
            <div class="booking-detail">
                <div class="booking-header">
                    <div class="booking-title-section">
                        <h2 class="booking-title">Booking #<?php echo $bookingData['id']; ?></h2>
                        <?php if (!empty($bookingData['reference'])): ?>
                            <div class="reference-display">
                                <span class="reference-label">Reference:</span>
                                <span class="reference-number"><?php echo htmlspecialchars($bookingData['reference']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="booking-meta">
                        <span><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($bookingData['created_at'])); ?></span>
                        <span class="booking-status status-<?php echo $bookingData['status']; ?>">
                            <?php echo ucfirst($bookingData['status']); ?>
                        </span>
                    </div>
                </div>

                <div class="booking-info">
                    <div>
                        <div class="info-group">
                            <div class="info-label">Client Name</div>
                            <div class="info-value"><?php echo htmlspecialchars($bookingData['client_name']); ?></div>
                        </div>

                        <div class="info-group">
                            <div class="info-label">Email</div>
                            <div class="info-value"><?php echo htmlspecialchars($bookingData['client_email']); ?></div>
                        </div>

                        <div class="info-group">
                            <div class="info-label">Phone</div>
                            <div class="info-value"><?php echo htmlspecialchars($bookingData['client_phone']); ?></div>
                        </div>
                    </div>

                    <div>
                        <div class="info-group">
                            <div class="info-label">Service</div>
                            <div class="info-value">
                                <?php
                                if (isset($bookingData['service_name'])) {
                                    echo htmlspecialchars($bookingData['service_name']);
                                } else {
                                    echo 'Service Not Specified';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="info-group">
                            <div class="info-label">Date</div>
                            <div class="info-value"><?php echo date('F j, Y', strtotime($bookingData['booking_date'])); ?></div>
                        </div>

                        <div class="info-group">
                            <div class="info-label">Time</div>
                            <div class="info-value"><?php echo date('g:i A', strtotime($bookingData['booking_time'])); ?></div>
                        </div>
                    </div>

                    <div>
                        <div class="info-group">
                            <div class="info-label">Address</div>
                            <div class="info-value"><?php echo htmlspecialchars($bookingData['address']); ?></div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($bookingData['special_requests'])): ?>
                    <div class="booking-notes">
                        <h3>Special Requests</h3>
                        <p><?php echo nl2br(htmlspecialchars($bookingData['special_requests'])); ?></p>
                    </div>
                <?php endif; ?>

                <div class="booking-actions">
                    <a href="bookings.php" class="btn-cancel">Back to List</a>
                    <a href="bookings.php?action=edit&id=<?php echo $bookingData['id']; ?>" class="btn-submit">Edit Booking</a>
                </div>
            </div>
        <?php else: ?>
            <!-- Booking Form -->
            <div class="booking-form">
                <form method="POST" action="bookings.php">                    <input type="hidden" name="action" value="<?php echo $action; ?>">
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="id" value="<?php echo $bookingData['id']; ?>">
                    <?php endif; ?>

                    <div class="form-grid">
                        <!-- Reference Number Field -->
                        <div class="form-group">
                            <label for="reference">Booking Reference</label>
                            <input type="text" id="reference" name="reference" 
                                   value="<?php echo $action === 'edit' ? htmlspecialchars($bookingData['reference'] ?? '') : ''; ?>" 
                                   placeholder="e.g., BK20250623ABC123"
                                   <?php echo $action === 'add' ? '' : 'readonly'; ?>>
                            <small class="text-muted">
                                <?php if ($action === 'add'): ?>
                                    Leave blank to auto-generate a reference number
                                <?php else: ?>
                                    Reference number cannot be changed after creation
                                <?php endif; ?>
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="client_name">Client Name</label>
                            <input type="text" id="client_name" name="client_name" value="<?php echo $action === 'edit' ? htmlspecialchars($bookingData['client_name']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="client_email">Email</label>
                            <input type="email" id="client_email" name="client_email" value="<?php echo $action === 'edit' ? htmlspecialchars($bookingData['client_email']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="client_phone">Phone</label>
                            <input type="text" id="client_phone" name="client_phone" value="<?php echo $action === 'edit' ? htmlspecialchars($bookingData['client_phone']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="service_id">Service</label>
                            <select id="service_id" name="service_id" required>
                                <option value="">Select a service</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?php echo $service['id']; ?>" <?php echo ($action === 'edit' && $bookingData['service_id'] == $service['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($service['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="booking_date">Date</label>
                            <input type="date" id="booking_date" name="booking_date" value="<?php echo $action === 'edit' ? $bookingData['booking_date'] : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="booking_time">Time</label>
                            <input type="time" id="booking_time" name="booking_time" value="<?php echo $action === 'edit' ? $bookingData['booking_time'] : ''; ?>" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address">Address</label>
                        <textarea id="address" name="address" required><?php echo $action === 'edit' ? htmlspecialchars($bookingData['address']) : ''; ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="special_requests">Special Requests</label>
                        <textarea id="special_requests" name="special_requests"><?php echo $action === 'edit' ? htmlspecialchars($bookingData['special_requests']) : ''; ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="pending" <?php echo ($action === 'edit' && $bookingData['status'] === 'pending') ? 'selected' : ''; ?>>Pending</option>
                            <option value="confirmed" <?php echo ($action === 'edit' && $bookingData['status'] === 'confirmed') ? 'selected' : ''; ?>>Confirmed</option>
                            <option value="completed" <?php echo ($action === 'edit' && $bookingData['status'] === 'completed') ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo ($action === 'edit' && $bookingData['status'] === 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <a href="bookings.php" class="btn-cancel">Cancel</a>
                        <button type="submit" class="btn-submit">
                            <?php echo $action === 'add' ? 'Add Booking' : 'Update Booking'; ?>
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });

        // Confirm delete
        function confirmDelete(id, name) {
            if (confirm(`Are you sure you want to delete the booking for "${name}"?`)) {
                window.location.href = `bookings.php?action=delete&id=${id}`;
            }
        }
    </script>
</body>
</html>
